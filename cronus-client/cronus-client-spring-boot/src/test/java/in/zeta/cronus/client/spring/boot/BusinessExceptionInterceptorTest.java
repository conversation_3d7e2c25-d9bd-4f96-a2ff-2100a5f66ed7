package in.zeta.cronus.client.spring.boot;

import static in.zeta.cronus.client.spring.boot.MockBeanConfig.ExceptionTestBean.idempotentKeyCompletionStage;
import static in.zeta.cronus.client.spring.boot.MockBeanConfig.ExceptionTestBean.idempotentKeySimpleMethod;
import static in.zeta.cronus.client.spring.boot.MockBeanConfig.ExceptionTestBean.idempotentKeyWithCompletionsStageController;
import static in.zeta.cronus.client.spring.boot.MockBeanConfig.ExceptionTestBean.idempotentKeyWithExceptionController;

import in.zeta.cronus.client.spring.boot.MockBeanConfig.MockBusinessExceptionHandler;
import in.zeta.cronus.client.spring.boot.MockBeanConfig.ExceptionTestBean;
import in.zeta.cronus.client.spring.boot.interceptor.BusinessExceptionMethodAdvice;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.aop.aspectj.annotation.AnnotationAwareAspectJAutoProxyCreator;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.context.annotation.Import;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.request.MockMvcRequestBuilders;
import org.springframework.test.web.servlet.result.MockMvcResultMatchers;

@RunWith(SpringRunner.class)
@SpringBootTest
@AutoConfigureMockMvc
@Import({AnnotationAwareAspectJAutoProxyCreator.class, BusinessExceptionMethodAdvice.class})
public class BusinessExceptionInterceptorTest {

  @Autowired
  private MockMvc mockMvc;

  @Autowired
  private ExceptionTestBean testExceptionBean;

  @Autowired
  private BusinessExceptionMethodAdvice businessExceptionSpringInterceptor;

  @Before
  public void setup() {
    MockBusinessExceptionHandler.reset();
  }

  @Test
  public void testInterceptor() throws Exception {
    try {
      mockMvc.perform(MockMvcRequestBuilders.get("/simpleMethod"))
          .andExpect(MockMvcResultMatchers.status().is(500));
    } catch (Exception e) {
      Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.containsKey(
          idempotentKeyWithExceptionController));
    }
  }

  @Test
  public void testInterceptorFuture() throws Exception {
    try {
      mockMvc.perform(MockMvcRequestBuilders.get("/futureMethod"))
          .andExpect(MockMvcResultMatchers.status().is(200));
    } catch (Exception e) {
      Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.containsKey(
          idempotentKeyWithCompletionsStageController));
    }
  }

  @Test
  public void testInterceptorWithException() throws Exception {
    try {
      testExceptionBean.exceptionInNormalMethod();
    } catch (Exception e) {
      Assert.assertTrue(
          MockBusinessExceptionHandler.mockPublish.containsKey(idempotentKeySimpleMethod));
    }
  }

  @Test
  public void testInterceptorWithExceptionCompletionStage() throws Exception {
    try {
      testExceptionBean.exceptionInFuture();
    } catch (Exception e) {
      Assert.assertTrue(
          MockBusinessExceptionHandler.mockPublish.containsKey(idempotentKeyCompletionStage));
    }
  }


}
