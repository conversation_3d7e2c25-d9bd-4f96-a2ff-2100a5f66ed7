package in.zeta.cronus.client.spring.boot;

import in.zeta.cronus.client.base.BusinessExceptionBaseInterceptor;
import in.zeta.cronus.client.base.BusinessExceptionHandler;
import in.zeta.cronus.client.models.BusinessException;
import in.zeta.cronus.client.models.CloseException;
import in.zeta.cronus.client.models.CronusBusinessException;
import in.zeta.cronus.client.models.annotations.PublishBusinessException;
import in.zeta.cronus.client.spring.boot.interceptor.BusinessExceptionRequestInterceptor;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.springframework.boot.SpringBootConfiguration;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.stereotype.Component;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
@SpringBootConfiguration
public class MockBeanConfig {

  @Bean
  public MockBusinessExceptionHandler testInterceptor() {
    return new MockBusinessExceptionHandler();
  }

  @Bean
  public BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor() {
    return new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
  }

  @Configuration
  @EnableWebMvc
  public static class TestWebConfig implements WebMvcConfigurer {

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
      registry.addInterceptor(new BusinessExceptionRequestInterceptor(
          new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler())));
    }
  }

  @RestController
  public static class TestExceptionBean {

    @GetMapping("/simpleMethod")
    public String exampleMethod() {
      throw ExceptionTestBean.getBusinessExceptionInstance(
          ExceptionTestBean.idempotentKeyWithExceptionController);
    }

    @GetMapping("/futureMethod")
    public CompletionStage<String> futureMethod() {
      return CompletableFuture.supplyAsync(() -> {
        throw ExceptionTestBean.getBusinessExceptionInstance(
            ExceptionTestBean.idempotentKeyWithCompletionsStageController);
      });
    }

  }

  public static class MockBusinessExceptionHandler implements BusinessExceptionHandler {

    public static final Map<String, Object> mockPublish = new HashMap<>();

    public static void reset() {
      mockPublish.clear();
    }

    @Override
    public CompletionStage<Void> publish(BusinessException BusinessExceptionInstance) {
      mockPublish.put(BusinessExceptionInstance.getIdempotentKey(), BusinessExceptionInstance);
      return CompletableFuture.runAsync(() -> {
      });
    }

    @Override
    public CompletionStage<Void> close(CloseException closeException) {
      mockPublish.put(closeException.getIdempotentKey(), closeException);
      return CompletableFuture.runAsync(() -> {
      });
    }
  }

  @Component
  public static class ExceptionTestBean {

    public static String idempotentKeySimpleMethod = "idempotentKeySimpleMethod";
    public static String idempotentKeyCompletionStage = "idempotentKeyCompletionStage";
    public static String idempotentKeyWithExceptionController = "idempotentKeyWithExceptionController";
    public static String idempotentKeyWithCompletionsStageController = "idempotentKeyWithCompletionsStageController";

    public static CronusBusinessException getBusinessExceptionInstance(String idempotentKey) {
      return CronusBusinessException
          .builder()
          .idempotentKey(idempotentKey)
          .tenantId("tenantId")
          .businessExceptionDefinitionCode("businessExceptionDefinitionCode")
          .exceptionInfo(new HashMap<>())
          .build();
    }

    @PublishBusinessException
    public CompletionStage<String> exceptionInFuture() {
      return CompletableFuture.supplyAsync(() -> {
        throw getBusinessExceptionInstance(idempotentKeyCompletionStage);
      });
    }

    @PublishBusinessException
    public void exceptionInNormalMethod() {
      throw getBusinessExceptionInstance(idempotentKeySimpleMethod);
    }
  }

}