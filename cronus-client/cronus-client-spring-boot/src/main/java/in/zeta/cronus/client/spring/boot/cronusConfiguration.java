package in.zeta.cronus.client.spring.boot;

import com.google.gson.Gson;
import in.zeta.cronus.client.base.BusinessExceptionBaseInterceptor;
import in.zeta.cronus.client.base.BusinessExceptionHandler;
import in.zeta.cronus.client.base.BusinessExceptionValidator;
import in.zeta.cronus.client.base.BusinessObservationHandler;
import in.zeta.cronus.client.base.publisher.AtroposBusinessExceptionInstanceClient;
import in.zeta.cronus.client.base.publisher.AtroposBusinessObservationHandler;
import in.zeta.oms.atropos.client.AtroposPublisherClient;
import olympus.common.JID;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import java.util.Random;

@Configuration
public class cronusConfiguration {

  private final Random random = new Random();

  @Bean
  public BusinessExceptionHandler getBusinessExceptionInstancePublisher(
      AtroposPublisherClient atroposPublisherClient,
      BusinessExceptionValidator businessExceptionValidator) {
    return new AtroposBusinessExceptionInstanceClient(
        atroposPublisherClient, businessExceptionValidator);
  }

  @Bean
  @ConditionalOnMissingBean
  public BusinessObservationHandler getBusinessObservationHandler(
      AtroposPublisherClient atroposPublisherClient) {
    return new AtroposBusinessObservationHandler(atroposPublisherClient, new Gson()) ;
  }

  @Bean
  public BusinessExceptionValidator getBusinessExceptionValidator() {
    return new BusinessExceptionValidator();
  }

  @Bean
  @ConditionalOnMissingBean
  public AtroposPublisherClient getAtroposPublisherClient(@Value("${app.name}") String appName) {
    return new AtroposPublisherClient(
        new JID(random.nextInt(100000) + "@" + appName + ".services.olympus"));
  }

  @Bean
  public BusinessExceptionBaseInterceptor getBusinessExceptionBaseInterceptor(
      BusinessExceptionHandler businessExceptionHandler) {
    return new BusinessExceptionBaseInterceptor(businessExceptionHandler);
  }

}
