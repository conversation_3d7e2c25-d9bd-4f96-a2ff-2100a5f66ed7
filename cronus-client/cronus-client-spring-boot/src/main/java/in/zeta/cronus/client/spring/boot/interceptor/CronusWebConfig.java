package in.zeta.cronus.client.spring.boot.interceptor;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class CronusWebConfig implements WebMvcConfigurer {
  private final BusinessExceptionRequestInterceptor businessExceptionRequestInterceptor;

  public CronusWebConfig(
      BusinessExceptionRequestInterceptor businessExceptionRequestInterceptor) {
    this.businessExceptionRequestInterceptor = businessExceptionRequestInterceptor;
  }

  @Override
  public void addInterceptors(InterceptorRegistry registry) {
    registry.addInterceptor(businessExceptionRequestInterceptor);
  }
}
