package in.zeta.cronus.client.spring.boot.interceptor;

import static in.zeta.commons.concurrency.CompletableFutures.unwrapCompletionStateException;

import in.zeta.cronus.client.base.BusinessExceptionBaseInterceptor;
import java.util.Objects;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.AsyncHandlerInterceptor;

@Component
public class BusinessExceptionRequestInterceptor implements AsyncHandlerInterceptor {

  private final BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor;

  public BusinessExceptionRequestInterceptor(
      BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor) {
    this.businessExceptionBaseInterceptor = businessExceptionBaseInterceptor;
  }

  @Override
  public void afterCompletion(HttpServletRequest request, HttpServletResponse response,
      Object handler, Exception ex) throws Exception {
    if (Objects.nonNull(ex)) {
      businessExceptionBaseInterceptor.publish(unwrapCompletionStateException(ex));
    }
  }

}
