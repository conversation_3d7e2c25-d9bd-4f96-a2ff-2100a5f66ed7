package in.zeta.cronus.client.spring.boot.interceptor;

import in.zeta.cronus.client.base.BusinessExceptionBaseInterceptor;
import java.lang.reflect.Method;
import java.util.concurrent.CompletionStage;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.stereotype.Component;

@Aspect
@Component
public class BusinessExceptionMethodAdvice {

  private final BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor;

  public BusinessExceptionMethodAdvice(
      BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor) {
    this.businessExceptionBaseInterceptor = businessExceptionBaseInterceptor;
  }

  @Around("@annotation(in.zeta.cronus.client.models.annotations.PublishBusinessException)")
  public Object invoke(ProceedingJoinPoint proceedingJoinPoint) throws Throwable {
    MethodSignature signature = (MethodSignature) proceedingJoinPoint.getSignature();
    Method method = signature.getMethod();
    Class<?> returnType = method.getReturnType();
    if (CompletionStage.class.isAssignableFrom(returnType)) {
      return businessExceptionBaseInterceptor.interceptFutureMethod(proceedingJoinPoint);
    }
    return businessExceptionBaseInterceptor.interceptMethod(proceedingJoinPoint);
  }

}
