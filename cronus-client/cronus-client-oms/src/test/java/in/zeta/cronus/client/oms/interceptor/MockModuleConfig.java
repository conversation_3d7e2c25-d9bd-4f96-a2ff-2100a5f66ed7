package in.zeta.cronus.client.oms.interceptor;


import static com.google.inject.matcher.Matchers.annotatedWith;
import static com.google.inject.matcher.Matchers.any;

import com.google.inject.AbstractModule;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import in.zeta.cronus.client.base.BusinessExceptionBaseInterceptor;
import in.zeta.cronus.client.base.BusinessExceptionHandler;
import in.zeta.cronus.client.models.BusinessException;
import in.zeta.cronus.client.models.CloseException;
import in.zeta.cronus.client.models.CronusBusinessException;
import in.zeta.cronus.client.models.annotations.PublishBusinessException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public class MockModuleConfig extends AbstractModule {


  @Override
  protected void configure() {
    BusinessExceptionMethodInterceptor businessExceptionMethodInterceptor = new BusinessExceptionMethodInterceptor();
    requestInjection(businessExceptionMethodInterceptor);
    bindInterceptor(any(), annotatedWith(PublishBusinessException.class),
        businessExceptionMethodInterceptor);
    bind(ExceptionGuiceBean.class);
  }


  @Provides
  @Singleton
  public BusinessExceptionHandler getBusinessExceptionHandler() {
    return new MockBusinessExceptionHandler();
  }

  @Provides
  @Singleton
  public BusinessExceptionBaseInterceptor getBusinessExceptionBaseInterceptor(
      BusinessExceptionHandler businessExceptionHandler) {
    return new BusinessExceptionBaseInterceptor(businessExceptionHandler);
  }

  public static class MockBusinessExceptionHandler implements BusinessExceptionHandler {

    public static final Map<String, Object> mockPublish = new HashMap<>();

    public static void reset() {
      mockPublish.clear();
    }

    @Override
    public CompletionStage<Void> publish(BusinessException BusinessExceptionInstance) {
      mockPublish.put(BusinessExceptionInstance.getIdempotentKey(), BusinessExceptionInstance);
      return CompletableFuture.runAsync(() -> {
      });
    }

    @Override
    public CompletionStage<Void> close(CloseException closeException) {
      mockPublish.put(closeException.getIdempotentKey(), closeException);
      return CompletableFuture.runAsync(() -> {
      });
    }
  }

  public static class ExceptionGuiceBean {

    public static String idempotentKeySimpleMethod = "idempotentKeySimpleMethod";
    public static String idempotentKeyCompletionStage = "idempotentKeyCompletionStage";

    public static CronusBusinessException getBusinessExceptionInstance(String idempotentKey) {
      return CronusBusinessException
          .builder()
          .idempotentKey(idempotentKey)
          .tenantId("tenantId")
          .businessExceptionDefinitionCode("businessExceptionDefinitionCode")
          .exceptionInfo(new HashMap<>())
          .build();
    }

    @PublishBusinessException
    public CompletionStage<String> exceptionInFuture() {
      return CompletableFuture.supplyAsync(() -> {
        throw getBusinessExceptionInstance(idempotentKeyCompletionStage);
      });
    }

    @PublishBusinessException
    public CompletionStage<String> technicalExceptionInFuture() {
      return CompletableFuture.supplyAsync(() -> {
        throw new RuntimeException();
      });
    }

    @PublishBusinessException
    public void exceptionInNormalMethod() {
      throw getBusinessExceptionInstance(idempotentKeySimpleMethod);
    }

    @PublishBusinessException
    public void exceptionInOtherException() {
      throw new RuntimeException();
    }

  }
}
