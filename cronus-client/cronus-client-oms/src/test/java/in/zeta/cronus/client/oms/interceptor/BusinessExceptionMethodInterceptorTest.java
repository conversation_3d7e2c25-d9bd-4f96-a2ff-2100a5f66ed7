package in.zeta.cronus.client.oms.interceptor;


import static in.zeta.cronus.client.oms.interceptor.MockModuleConfig.ExceptionGuiceBean.idempotentKeyCompletionStage;
import static in.zeta.cronus.client.oms.interceptor.MockModuleConfig.ExceptionGuiceBean.idempotentKeySimpleMethod;

import com.google.inject.Guice;
import com.google.inject.Injector;
import in.zeta.cronus.client.oms.interceptor.MockModuleConfig.MockBusinessExceptionHandler;
import in.zeta.cronus.client.oms.interceptor.MockModuleConfig.ExceptionGuiceBean;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;

public class BusinessExceptionMethodInterceptorTest {


  private Injector injector;

  @Before
  public void setUp() throws Exception {
    injector = Guice.createInjector(
        new MockModuleConfig()
    );
    MockBusinessExceptionHandler.reset();
  }

  @Test
  public void testInterceptorWithException() throws Exception {
    ExceptionGuiceBean testExceptionBean = injector.getInstance(ExceptionGuiceBean.class);
    try {
      testExceptionBean.exceptionInNormalMethod();
    } catch (Exception e) {
      Assert.assertTrue(
          MockBusinessExceptionHandler.mockPublish.containsKey(idempotentKeySimpleMethod));
    }
  }

  @Test
  public void testInterceptorWithTechnicalException() throws Exception {
    ExceptionGuiceBean testExceptionBean = injector.getInstance(ExceptionGuiceBean.class);
    try {
      testExceptionBean.exceptionInOtherException();
    } catch (Exception e) {
      Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.isEmpty());
    }
  }

  @Test
  public void testInterceptorWithCompletionStage() throws Exception {
    ExceptionGuiceBean testExceptionBean = injector.getInstance(ExceptionGuiceBean.class);
    try {
      testExceptionBean.exceptionInFuture().toCompletableFuture().join();
    } catch (Exception e) {
      Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.containsKey(idempotentKeyCompletionStage));
    }
  }

  @Test
  public void testTechnicalInterceptorWithCompletionStage() throws Exception {
    ExceptionGuiceBean testExceptionBean = injector.getInstance(ExceptionGuiceBean.class);
    try {
      testExceptionBean.technicalExceptionInFuture().toCompletableFuture().join();
    } catch (Exception e) {
      Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.isEmpty());
    }
  }

}