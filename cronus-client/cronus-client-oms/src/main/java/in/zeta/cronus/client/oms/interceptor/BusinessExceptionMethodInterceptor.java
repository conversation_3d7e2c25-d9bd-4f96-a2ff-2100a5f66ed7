package in.zeta.cronus.client.oms.interceptor;

import com.google.inject.Inject;
import com.google.inject.Singleton;
import in.zeta.cronus.client.base.BusinessExceptionBaseInterceptor;
import in.zeta.cronus.client.base.BusinessExceptionHandler;
import in.zeta.cronus.client.models.annotations.PublishBusinessException;
import java.util.Objects;
import java.util.concurrent.CompletionStage;
import org.aopalliance.intercept.MethodInterceptor;
import org.aopalliance.intercept.MethodInvocation;

@Singleton
public class BusinessExceptionMethodInterceptor implements MethodInterceptor {

  @Inject
  private BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor;

  public BusinessExceptionMethodInterceptor() {

  }

  @Inject
  public BusinessExceptionMethodInterceptor(
      BusinessExceptionHandler businessExceptionHandler) {
    this.businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(
        businessExceptionHandler);
  }

  @Override
  public Object invoke(MethodInvocation methodInvocation) throws Throwable {
    PublishBusinessException publishBusinessException = methodInvocation.getMethod()
        .getAnnotation(PublishBusinessException.class);
    if (Objects.nonNull(publishBusinessException) && Objects.nonNull(businessExceptionBaseInterceptor)) {
      return intercept(methodInvocation, publishBusinessException);
    }
    return methodInvocation.proceed();
  }

  private Object intercept(MethodInvocation methodInvocation,
      PublishBusinessException publishBusinessException) throws Throwable {
    Class<?> returnType = methodInvocation.getMethod().getReturnType();
    if (CompletionStage.class.isAssignableFrom(returnType)) {
      return businessExceptionBaseInterceptor.interceptFutureMethod(methodInvocation);
    }
    return businessExceptionBaseInterceptor.interceptMethod(methodInvocation);
  }

}
