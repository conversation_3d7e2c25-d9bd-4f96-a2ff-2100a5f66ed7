package in.zeta.cronus.client.oms.config;

import static com.google.inject.matcher.Matchers.annotatedWith;
import static com.google.inject.matcher.Matchers.any;
import com.google.gson.Gson;
import com.google.inject.Provides;
import com.google.inject.Singleton;
import in.zeta.commons.settings.SettingsModule;
import in.zeta.cronus.client.base.BusinessExceptionBaseInterceptor;
import in.zeta.cronus.client.base.BusinessExceptionHandler;
import in.zeta.cronus.client.base.BusinessExceptionValidator;
import in.zeta.cronus.client.base.BusinessObservationHandler;
import in.zeta.cronus.client.base.publisher.AtroposBusinessExceptionInstanceClient;
import in.zeta.cronus.client.base.publisher.AtroposBusinessObservationHandler;
import in.zeta.cronus.client.models.annotations.PublishBusinessException;
import in.zeta.cronus.client.oms.interceptor.BusinessExceptionMethodInterceptor;
import in.zeta.oms.atropos.client.AtroposPublisherClient;
import java.util.Random;
import javax.inject.Named;
import olympus.common.JID;

public class BusinessExceptionModule extends SettingsModule {

  private final Random random = new Random();

  @Override
  protected void configure() {
    super.configure();
    BusinessExceptionMethodInterceptor businessExceptionMethodInterceptor = new BusinessExceptionMethodInterceptor();
    requestInjection(businessExceptionMethodInterceptor);
    bindInterceptor(any(), annotatedWith(PublishBusinessException.class),
        businessExceptionMethodInterceptor);
  }

  @Provides
  @Singleton
  public AtroposPublisherClient getAtroposPublisherClient(@Named("app.name") String appName) {
    return new AtroposPublisherClient(
        new JID(random.nextInt(100000) + "@" + appName + ".services.olympus"));
  }

  @Provides
  @Singleton
  public BusinessExceptionValidator getBusinessExceptionValidator() {
    return new BusinessExceptionValidator();
  }

  @Provides
  @Singleton
  public BusinessExceptionHandler getBusinessExceptionHandler(
      AtroposPublisherClient atroposPublisherClient,
      BusinessExceptionValidator businessExceptionValidator) {
    return new AtroposBusinessExceptionInstanceClient(
        atroposPublisherClient, businessExceptionValidator);
  }

  @Provides
  @Singleton
  public BusinessObservationHandler getBusinessObservationHandler(
      AtroposPublisherClient atroposPublisherClient) {
    return new AtroposBusinessObservationHandler(atroposPublisherClient, new Gson()) ;
  }

  @Provides
  @Singleton
  public BusinessExceptionBaseInterceptor getBusinessExceptionBaseInterceptor(
      BusinessExceptionHandler businessExceptionHandler) {
    return new BusinessExceptionBaseInterceptor(businessExceptionHandler);
  }
}
