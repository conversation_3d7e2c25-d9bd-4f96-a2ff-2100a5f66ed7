<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <artifactId>cronus-client</artifactId>
        <groupId>in.zeta</groupId>
        <version>1.0.12-SNAPSHOT</version>
    </parent>
    <dependencies>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.17.0</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>
    <modelVersion>4.0.0</modelVersion>
    <packaging>jar</packaging>

    <artifactId>cronus-client-models</artifactId>
    <version>1.0.12-SNAPSHOT</version>
</project>