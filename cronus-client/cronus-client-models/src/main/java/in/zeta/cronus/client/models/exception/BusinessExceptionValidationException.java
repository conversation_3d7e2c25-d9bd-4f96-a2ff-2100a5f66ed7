package in.zeta.cronus.client.models.exception;

public class BusinessExceptionValidationException extends BusinessExceptionBaseException {
  public BusinessExceptionValidationException(String message) {
    super(message);
  }

  public BusinessExceptionValidationException(String message, Throwable throwable) {
    super(message, throwable);
  }

  public BusinessExceptionValidationException(Throwable throwable) {
    super(throwable);
  }
}
