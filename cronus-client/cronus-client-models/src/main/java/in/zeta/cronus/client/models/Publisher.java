package in.zeta.cronus.client.models;

import com.google.common.base.Strings;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Publisher {

  public static final String ENV_CLUSTER_NAME = "CLUSTER_NAME";
  public static final String PROPERTY_CLUSTER_NAME = "cluster.name";
  public static final String ENV_APP_NAME = "APP_NAME";
  public static final String PROPERTY_APP_NAME = "app.name";
  public static final String DEFAULT_CLUSTER = "default";
  public static final String DEFAULT_SERVICE = "unknown-service";
  private String clusterName;
  private String appName;

  public static Publisher getInstance() {
    return Publisher.builder()
        .clusterName(getClusterNameFromProperty())
        .appName(getAppNameFromProperty())
        .build();
  }

  public static Publisher getInstance(String clusterName, String appName) {
    return Publisher.builder()
        .clusterName(clusterName)
        .appName(appName)
        .build();
  }

  public static String getClusterNameFromProperty() {
    if (!Strings.isNullOrEmpty(System.getenv(ENV_CLUSTER_NAME))) {
      return System.getenv(ENV_CLUSTER_NAME);
    }
    if (System.getProperties().containsKey(PROPERTY_CLUSTER_NAME)) {
      return System.getProperty(PROPERTY_CLUSTER_NAME);
    }
    return DEFAULT_CLUSTER;
  }

  public static String getAppNameFromProperty() {
    if (!Strings.isNullOrEmpty(System.getenv(ENV_APP_NAME))) {
      return System.getenv(ENV_APP_NAME);
    }
    if (System.getProperties().containsKey(PROPERTY_APP_NAME)) {
      return System.getProperty(PROPERTY_APP_NAME);
    }
    return DEFAULT_SERVICE;
  }
}
