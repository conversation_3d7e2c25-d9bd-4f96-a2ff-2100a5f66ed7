package in.zeta.cronus.client.models;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Getter;
import lombok.NoArgsConstructor;

@Getter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BusinessObservationEvent {

  private Long tenantId;

  private String definitionCode;

  private Map<String, Object> observationInfo;

  private Publisher publisher;

  private String idempotentKey;

  private String olympusTraceId;

  private Long reportedTimeInMillis;


  public static BusinessObservationEvent from(BusinessObservation businessObservation){

    return BusinessObservationEvent.builder()
        .tenantId(Long.parseLong(businessObservation.getTenantId()))
        .definitionCode(businessObservation.getDefinitionCode())
        .observationInfo(businessObservation.getObservationInfo())
        .idempotentKey(businessObservation.getIdempotentKey())
        .definitionCode(businessObservation.getDefinitionCode())
        .publisher(Publisher.getInstance())
        .reportedTimeInMillis(System.currentTimeMillis())
        .build();
  }
}
