package in.zeta.cronus.client.models;


import lombok.Builder;
import lombok.Getter;

@Getter
@Builder
public class CloseBusinessException  extends RuntimeException implements CloseException {
  private final String tenantId;
  private final String businessExceptionDefinitionCode;
  private final String idempotentKey;
  private final ExceptionPublisherAction action;
  private final String closureRemark;

    private CloseBusinessException(String tenantId, String businessExceptionDefinitionCode, String idempotentKey, ExceptionPublisherAction action, String closureRemark) {
        this.tenantId = tenantId;
        this.businessExceptionDefinitionCode = businessExceptionDefinitionCode;
        this.idempotentKey = idempotentKey;
        this.action = action;
        this.closureRemark=closureRemark;
    }

    private CloseBusinessException() {
        this.tenantId = null;
        this.businessExceptionDefinitionCode = null;
        this.idempotentKey = null;
        this.action = null;
        this.closureRemark=null;
    }

    public static CloseBusinessException getInstance(String tenantId, String businessExceptionDefinitionCode, String idempotentKey, ExceptionPublisherAction action,String closureRemark) {
        return new CloseBusinessException(tenantId, businessExceptionDefinitionCode, idempotentKey, action,closureRemark);
    }
}
