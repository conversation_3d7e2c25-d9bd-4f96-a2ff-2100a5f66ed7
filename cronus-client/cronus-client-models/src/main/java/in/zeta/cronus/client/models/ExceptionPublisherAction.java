package in.zeta.cronus.client.models;

import com.fasterxml.jackson.annotation.JsonCreator;

public enum ExceptionPublisherAction {
    CLOSE;

    @JsonCreator
    public static ExceptionPublisherAction create(String text) {
        try {
            return ExceptionPublisherAction.valueOf(text);
        } catch (IllegalArgumentException ex) {
            throw new IllegalArgumentException("Invalid Source");
        }
    }
}
