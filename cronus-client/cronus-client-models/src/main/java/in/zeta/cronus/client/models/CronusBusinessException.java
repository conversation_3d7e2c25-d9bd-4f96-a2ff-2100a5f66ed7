package in.zeta.cronus.client.models;

import java.util.HashMap;
import lombok.Builder;
import lombok.Getter;
import java.util.Map;

@Getter
@Builder
public class CronusBusinessException extends RuntimeException  implements BusinessException{
  private final String tenantId;
  private final String businessExceptionDefinitionCode;
  private final String idempotentKey;
  private final Map<String, Object> exceptionInfo;

  private CronusBusinessException(String tenantId, String businessExceptionDefinitionCode, String idempotentKey, Map<String, Object> exceptionInfo) {
    this.tenantId = tenantId;
    this.businessExceptionDefinitionCode = businessExceptionDefinitionCode;
    this.idempotentKey = idempotentKey;
    this.exceptionInfo = exceptionInfo;
  }
  private CronusBusinessException() {
    this.tenantId = null;
    this.businessExceptionDefinitionCode = null;
    this.idempotentKey = null;
    this.exceptionInfo = new HashMap<>();
  }

  public static CronusBusinessException getInstance(String tenantId, String businessExceptionDefinitionCode, String idempotentKey, Map<String, Object> exceptionInfo) {
    return new CronusBusinessException(tenantId, businessExceptionDefinitionCode, idempotentKey, exceptionInfo);
  }
}
