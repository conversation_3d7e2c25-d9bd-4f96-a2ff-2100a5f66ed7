package in.zeta.cronus.client.models;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.util.Map;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
public class BusinessExceptionInstanceEvent {
  private String tenantId;
  private String idempotencyKey;
  private String publisherAppName;
  private String publisherClusterName;
  private String businessExceptionDefinitionCode;
  private Long reportedTimeInMillis;
  private Map<String, Object> businessExceptionInfo;
}
