package in.zeta.cronus.client.models;

import java.util.Map;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.NoArgsConstructor;

@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DefaultBusinessObservation implements BusinessObservation {

  private String tenantId;
  private String definitionCode;
  private Map<String, Object> observationInfo;
  private String idempotentKey;
  private String remarks;

  @Override
  public String getTenantId() {
    return this.tenantId;
  }

  @Override
  public String getDefinitionCode() {
    return this.definitionCode;
  }

  @Override
  public String getIdempotentKey() {
    return this.idempotentKey;
  }

  @Override
  public Map<String, Object> getObservationInfo() {
    return this.observationInfo;
  }

  @Override
  public String getRemarks() {
    return  this.remarks;
  }


  public static BusinessObservation getObservationInstance(String tenantId, String definitionCode,
      String idempotentKey, Map<String, Object> observationInfo) {
    return new DefaultBusinessObservation(tenantId, definitionCode, observationInfo, idempotentKey,"");
  }

  public static BusinessObservation getObservationInstance(String tenantId, String definitionCode,
      String idempotentKey, Map<String, Object> observationInfo, String remarks) {
    return new DefaultBusinessObservation(tenantId, definitionCode, observationInfo, idempotentKey, remarks);
  }
}
