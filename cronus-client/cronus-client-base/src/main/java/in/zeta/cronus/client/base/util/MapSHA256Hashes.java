package in.zeta.cronus.client.base.util;

import com.google.gson.Gson;
import java.util.Map;
import java.util.TreeMap;

public class MapSHA256Hashes {

    public static String calculateSHA256Hash(Map<String, Object> map) {
            // Calculate the SHA-256 hash of the JSON string
            return org.apache.commons.codec.digest.DigestUtils.sha1Hex(getMapAsString(map));
    }

    private static String getMapAsString(Map<String, Object> map) {
        // Sort the map by keys to ensure consistent hash calculation
        Map<String, Object> sortedMap = map instanceof TreeMap? map : new TreeMap<>(map);
        // Convert the sorted map to a JSON string
        Gson objectMapper = new Gson();
        return objectMapper.toJson(sortedMap);
    }
}
