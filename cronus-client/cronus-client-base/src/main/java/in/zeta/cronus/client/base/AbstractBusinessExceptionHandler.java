package in.zeta.cronus.client.base;

import in.zeta.cronus.client.base.util.MapSHA256Hashes;
import in.zeta.cronus.client.models.*;

import java.util.Objects;
import java.util.TreeMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public abstract class AbstractBusinessExceptionHandler implements BusinessExceptionHandler {

  public static final String BUSINESS_EXCEPTION_TENANT_ID = "businessExceptionTenantID";
  public static final String BUSINESS_EXCEPTION_DEFINITION_CODE = "businessExceptionDefinitionCode";
  private final BusinessExceptionValidator businessExceptionValidator;

  protected AbstractBusinessExceptionHandler(BusinessExceptionValidator businessExceptionValidator) {
    this.businessExceptionValidator = businessExceptionValidator;
  }

  public CompletionStage<Void> publish(BusinessException businessExceptionInstance) {
    return enrichBusinessExceptionInstance(businessExceptionInstance)
        .thenCompose(this::publishBusinessExceptionInstanceEvent);
  }

  public CompletionStage<Void> close(CloseException closeException) {
    return enrichCloseExceptionInstance(closeException)
            .thenCompose(this::publishCloseExceptionInstanceEvent);
  }

  protected abstract CompletionStage<Void> publishBusinessExceptionInstanceEvent(
      BusinessExceptionInstanceEvent businessExceptionInstanceEvent);

  protected abstract CompletionStage<Void> publishCloseExceptionInstanceEvent(
          CloseExceptionInstanceEvent closeExceptionInstanceEvent);

  protected CompletionStage<BusinessExceptionInstanceEvent> enrichBusinessExceptionInstance(
      BusinessException businessExceptionInstance) {
    BusinessExceptionInstanceEvent businessExceptionInstanceEvent =
        new BusinessExceptionInstanceEvent();
    businessExceptionInstanceEvent.setIdempotencyKey(
        generateIdempotencyKey(businessExceptionInstance));
    businessExceptionInstanceEvent.setBusinessExceptionInfo(
        businessExceptionInstance.getExceptionInfo());
    businessExceptionInstanceEvent.setBusinessExceptionDefinitionCode(
        businessExceptionInstance.getBusinessExceptionDefinitionCode());
    businessExceptionInstanceEvent.setPublisherAppName(Publisher.getAppNameFromProperty());
    businessExceptionInstanceEvent.setPublisherClusterName(Publisher.getClusterNameFromProperty());
    businessExceptionInstanceEvent.setReportedTimeInMillis(System.currentTimeMillis());
    businessExceptionInstanceEvent.setTenantId(businessExceptionInstance.getTenantId());
    return CompletableFuture.completedFuture(businessExceptionInstanceEvent);
  }

  protected CompletionStage<CloseExceptionInstanceEvent> enrichCloseExceptionInstance(
          CloseException closeExceptionInstance) {
    CloseExceptionInstanceEvent closeExceptionInstanceEvent =
            new CloseExceptionInstanceEvent();
    closeExceptionInstanceEvent.setIdempotencyKey(closeExceptionInstance.getIdempotentKey());

    closeExceptionInstanceEvent.setBusinessExceptionDefinitionCode(
            closeExceptionInstance.getBusinessExceptionDefinitionCode());
    closeExceptionInstanceEvent.setTenantId(closeExceptionInstance.getTenantId());
    return CompletableFuture.completedFuture(closeExceptionInstanceEvent);
  }

  protected String generateIdempotencyKey(BusinessException businessExceptionInstance) {
    if (Objects.nonNull(businessExceptionInstance.getIdempotentKey())) {
      return businessExceptionInstance.getIdempotentKey();
    }
    TreeMap<String, Object> requestPayload = new TreeMap<>(
        businessExceptionInstance.getExceptionInfo());
    requestPayload.put(BUSINESS_EXCEPTION_TENANT_ID, businessExceptionInstance.getTenantId());
    requestPayload.put(BUSINESS_EXCEPTION_DEFINITION_CODE,
        businessExceptionInstance.getBusinessExceptionDefinitionCode());
    return MapSHA256Hashes.calculateSHA256Hash(requestPayload);
  }

}
