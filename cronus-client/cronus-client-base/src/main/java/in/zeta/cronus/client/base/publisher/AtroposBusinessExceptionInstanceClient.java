package in.zeta.cronus.client.base.publisher;

import com.google.gson.Gson;
import in.zeta.cronus.client.base.AbstractBusinessExceptionHandler;
import in.zeta.cronus.client.base.BusinessExceptionValidator;
import in.zeta.cronus.client.models.BusinessExceptionInstanceEvent;
import in.zeta.cronus.client.models.CloseExceptionInstanceEvent;
import in.zeta.cronus.client.models.Publisher;
import in.zeta.cronus.client.models.exception.BusinessExceptionInstancePublishingException;
import in.zeta.oms.atropos.client.AtroposPublisherClient;
import in.zeta.spectra.capture.SpectraLogger;
import olympus.pubsub.model.OperationType;
import olympus.pubsub.model.PubSubEvent;
import olympus.pubsub.model.Tag;
import olympus.pubsub.model.TopicScope;
import olympus.trace.OlympusSpectra;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

import java.util.Arrays;
import java.util.concurrent.CompletionStage;

import static in.zeta.commons.concurrency.CompletableFutures.unwrapCompletionStateException;

public class AtroposBusinessExceptionInstanceClient
    extends AbstractBusinessExceptionHandler {
  private final SpectraLogger logger = OlympusSpectra.getLogger(AtroposBusinessExceptionInstanceClient.class);

  private Gson gson = new Gson();
  private AtroposPublisherClient atroposPublisherClient;
  private static final String TOPIC_NAME_FORMAT = "_%s_%s_%s";
  private static final String objectType = "BusinessException";
  private static final String objectTypeClose = "BusinessExceptionUpdate";

  public AtroposBusinessExceptionInstanceClient(
      AtroposPublisherClient atroposPublisherClient,
      BusinessExceptionValidator businessExceptionValidator) {
    super(businessExceptionValidator);
    this.atroposPublisherClient = atroposPublisherClient;
  }

  @Override
  protected CompletionStage<Void> publishBusinessExceptionInstanceEvent(
      BusinessExceptionInstanceEvent businessExceptionInstance) {
    PubSubEvent.Builder event = constructPubSubEventBuilder(businessExceptionInstance);
    String topic =
        String.format(
            TOPIC_NAME_FORMAT,
            event.getTopicScope().name().toLowerCase(),
            event.getTenant(),
            event.getObjectType());
    logInfo(businessExceptionInstance,"Commenced Exception Publication",topic);
    return atroposPublisherClient
        .publish(event)
        .thenAccept(
            aVoid -> logInfo(businessExceptionInstance,"Successfully Exception Published",topic))
        .exceptionally(
            throwable -> {
              throwable = unwrapCompletionStateException(throwable);
              logError(throwable, businessExceptionInstance);
              throw new BusinessExceptionInstancePublishingException(throwable);
            });
  }

  @Override
  protected CompletionStage<Void> publishCloseExceptionInstanceEvent(
          CloseExceptionInstanceEvent closeExceptionInstanceEvent) {
    PubSubEvent.Builder event = constructPubSubCloseEventBuilder(closeExceptionInstanceEvent);
    String topic =
            String.format(
                    TOPIC_NAME_FORMAT,
                    event.getTopicScope().name().toLowerCase(),
                    event.getTenant(),
                    event.getObjectType());
    logInfo(closeExceptionInstanceEvent,"Commenced Exception Publication",topic);
    return atroposPublisherClient
            .publish(event)
            .thenAccept(
                    aVoid -> logInfo(closeExceptionInstanceEvent,"Successfully Exception Published",topic))
            .exceptionally(
                    throwable -> {
                      throwable = unwrapCompletionStateException(throwable);
                      logError(throwable, closeExceptionInstanceEvent);
                      throw new BusinessExceptionInstancePublishingException(throwable);
                    });
  }

  private PubSubEvent.Builder constructPubSubEventBuilder(
      BusinessExceptionInstanceEvent businessExceptionInstanceEvent) {

    try {
      PubSubEvent.Builder event = new PubSubEvent.Builder();
      NameValuePair[] sourceAttributes = {
        new BasicNameValuePair("app", Publisher.getAppNameFromProperty()),
        new BasicNameValuePair("cluster", Publisher.getClusterNameFromProperty())
      };
      Tag tag =
          Tag.builder()
              .tagType("exceptionCode")
              .tagValue(businessExceptionInstanceEvent.getBusinessExceptionDefinitionCode())
              .attributes(sourceAttributes)
              .build();

      event
          .data(gson.toJsonTree(businessExceptionInstanceEvent))
          .tenant(String.valueOf(businessExceptionInstanceEvent.getTenantId()))
          .tags(Arrays.asList(tag))
          .objectType(objectType)
          .operationType(OperationType.CREATED)
          .objectID(getObjectId(businessExceptionInstanceEvent))
          .stateMachineState("NONE")
          .topicScope(TopicScope.TENANT)
          .sourceAttributes(sourceAttributes);
      return event;
    } catch (Throwable throwable) {
      throwable = unwrapCompletionStateException(throwable);
      logError(throwable, businessExceptionInstanceEvent);
      throw new BusinessExceptionInstancePublishingException(throwable);
    }
  }

  private PubSubEvent.Builder constructPubSubCloseEventBuilder(
          CloseExceptionInstanceEvent closeExceptionInstanceEvent) {

    try {
      PubSubEvent.Builder event = new PubSubEvent.Builder();
      NameValuePair[] sourceAttributes = {
              new BasicNameValuePair("app", Publisher.getAppNameFromProperty()),
              new BasicNameValuePair("cluster", Publisher.getClusterNameFromProperty())
      };
      Tag tag =
              Tag.builder()
                      .tagType("exceptionCode")
                      .tagValue(closeExceptionInstanceEvent.getBusinessExceptionDefinitionCode())
                      .attributes(sourceAttributes)
                      .build();

      event
              .data(gson.toJsonTree(closeExceptionInstanceEvent))
              .tenant(closeExceptionInstanceEvent.getTenantId())
              .tags(Arrays.asList(tag))
              .objectType(objectTypeClose)
              .operationType(OperationType.CREATED)
              .objectID(getObjectId(closeExceptionInstanceEvent))
              .stateMachineState("NONE")
              .topicScope(TopicScope.TENANT)
              .sourceAttributes(sourceAttributes);
      return event;
    } catch (Throwable throwable) {
      throwable = unwrapCompletionStateException(throwable);
      logError(throwable, closeExceptionInstanceEvent);
      throw new BusinessExceptionInstancePublishingException(throwable);
    }
  }


  private String getObjectId(Object eventInstance) {
    if (eventInstance instanceof BusinessExceptionInstanceEvent) {
      BusinessExceptionInstanceEvent event = (BusinessExceptionInstanceEvent) eventInstance;
      return event.getTenantId() + "." + event.getBusinessExceptionDefinitionCode();
    } else if (eventInstance instanceof CloseExceptionInstanceEvent) {
      CloseExceptionInstanceEvent event = (CloseExceptionInstanceEvent) eventInstance;
      return event.getTenantId() + "." + event.getBusinessExceptionDefinitionCode();
    }
    return null;
  }

  private void logError(Throwable throwable, Object eventInstance) {
    if (eventInstance instanceof BusinessExceptionInstanceEvent) {
      BusinessExceptionInstanceEvent event = (BusinessExceptionInstanceEvent) eventInstance;
      logger.error("Publish event error", throwable)
              .attr("exceptionCode", event.getBusinessExceptionInfo())
              .attr("tenantId", event.getTenantId())
              .attr("idempotentKey", event.getIdempotencyKey())
              .log();
    } else if (eventInstance instanceof CloseExceptionInstanceEvent) {
      CloseExceptionInstanceEvent event = (CloseExceptionInstanceEvent) eventInstance;
      logger.error("Publish event error", throwable)
              .attr("exceptionCode", event.getBusinessExceptionDefinitionCode())
              .attr("tenantId", event.getTenantId())
              .attr("idempotentKey", event.getIdempotencyKey())
              .log();
    }
  }

  private void logInfo(Object eventInstance, String title, String topic) {
    if (eventInstance instanceof BusinessExceptionInstanceEvent) {
      BusinessExceptionInstanceEvent event = (BusinessExceptionInstanceEvent) eventInstance;
      logger.info(title)
              .attr("exceptionCode", event.getBusinessExceptionInfo())
              .attr("topic", topic)
              .attr("tenantId", event.getTenantId())
              .attr("idempotentKey", event.getIdempotencyKey())
              .log();
    } else if (eventInstance instanceof CloseExceptionInstanceEvent) {
      CloseExceptionInstanceEvent event = (CloseExceptionInstanceEvent) eventInstance;
      logger.info(title)
              .attr("exceptionCode", event.getBusinessExceptionDefinitionCode())
              .attr("topic", topic)
              .attr("tenantId", event.getTenantId())
              .attr("idempotentKey", event.getIdempotencyKey())
              .log();
    }
  }
}
