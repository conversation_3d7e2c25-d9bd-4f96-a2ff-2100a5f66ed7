package in.zeta.cronus.client.base;

import static olympus.message.util.CompletableFutures.unwrapCompletionStateException;

import in.zeta.commons.concurrency.CompletableFutures;
import in.zeta.cronus.client.models.BusinessException;
import in.zeta.spectra.capture.SpectraLogger;
import java.util.concurrent.CompletionException;
import java.util.concurrent.CompletionStage;
import olympus.trace.OlympusSpectra;
import org.aopalliance.intercept.MethodInvocation;
import org.aspectj.lang.ProceedingJoinPoint;

public class BusinessExceptionBaseInterceptor {
  private static final SpectraLogger LOGGER = OlympusSpectra.getLogger(BusinessExceptionBaseInterceptor.class);

  private final BusinessExceptionHandler businessExceptionHandler;

  public BusinessExceptionBaseInterceptor(
      BusinessExceptionHandler businessExceptionHandler) {
    this.businessExceptionHandler = businessExceptionHandler;
  }

  public Object interceptFutureMethod(ProceedingJoinPoint methodInvocation)
      throws Throwable {
    return ((CompletionStage<?>) methodInvocation.proceed())
        .exceptionally(throwable -> {
          publish(unwrapCompletionStateException(throwable));
          throw new CompletionException(throwable);
        });
  }

  public Object interceptMethod(ProceedingJoinPoint methodInvocation) throws Throwable {
    try {
      return methodInvocation.proceed();
    } catch (Throwable throwable) {
      publish(unwrapCompletionStateException(throwable));
      throw throwable;
    }
  }

  public Object interceptFutureMethod(MethodInvocation methodInvocation)
      throws Throwable {
    return ((CompletionStage<?>) methodInvocation.proceed())
        .exceptionally(throwable -> {
          publish(CompletableFutures.unwrapCompletionStateException(throwable));
          throw new CompletionException(throwable);
        });
  }

  public Object interceptMethod(MethodInvocation methodInvocation) throws Throwable {
    try {
      return methodInvocation.proceed();
    } catch (Throwable throwable) {
      publish(CompletableFutures.unwrapCompletionStateException(throwable));
      throw throwable;
    }
  }

  public void publish(Throwable throwable) {
    if (throwable instanceof BusinessException) {
      try {
        businessExceptionHandler.publish((BusinessException) throwable);
      }catch (Throwable exception) {
        LOGGER.warn("Failed to publish BusinessException")
            .attr("tenantId", ((BusinessException) throwable).getTenantId())
            .attr("message", exception.getMessage())
            .attr("exceptionDefinitionCode", ((BusinessException) throwable).getBusinessExceptionDefinitionCode())
            .log();
      }
    }
  }
}
