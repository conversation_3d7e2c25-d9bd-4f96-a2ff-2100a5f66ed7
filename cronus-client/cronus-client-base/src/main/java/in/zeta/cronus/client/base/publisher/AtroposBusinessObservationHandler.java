package in.zeta.cronus.client.base.publisher;

import static in.zeta.commons.concurrency.CompletableFutures.unwrapCompletionStateException;

import com.google.gson.Gson;
import in.zeta.cronus.client.base.BusinessObservationHandler;
import in.zeta.cronus.client.models.BusinessObservation;
import in.zeta.cronus.client.models.BusinessObservationEvent;
import in.zeta.cronus.client.models.Publisher;
import in.zeta.cronus.client.models.exception.BusinessExceptionInstancePublishingException;
import in.zeta.oms.atropos.client.AtroposPublisherClient;
import in.zeta.spectra.capture.SpectraLogger;
import java.util.Arrays;
import java.util.concurrent.CompletionStage;
import olympus.pubsub.model.OperationType;
import olympus.pubsub.model.PubSubEvent;
import olympus.pubsub.model.Tag;
import olympus.pubsub.model.TopicScope;
import olympus.trace.OlympusSpectra;
import org.apache.http.NameValuePair;
import org.apache.http.message.BasicNameValuePair;

public class AtroposBusinessObservationHandler implements BusinessObservationHandler {

  private final SpectraLogger logger = OlympusSpectra.getLogger(
      AtroposBusinessObservationHandler.class);

  private final AtroposPublisherClient atroposPublisherClient;
  private static final String TOPIC_NAME_FORMAT = "_%s_%s_%s";
  private static final String objectType = "BusinessObservation";
  private static final String closeObjectType = "BusinessObservationClose";
  private final Gson gson;

  public AtroposBusinessObservationHandler(AtroposPublisherClient atroposPublisherClient,
      Gson gson) {
    this.atroposPublisherClient = atroposPublisherClient;
    this.gson = gson;
  }

  @Override
  public CompletionStage<Void> publish(BusinessObservation businessObservation) {
    PubSubEvent.Builder event = constructPubSubEventBuilder(
        BusinessObservationEvent.from(businessObservation),objectType);
    String topic =
        String.format(
            TOPIC_NAME_FORMAT,
            event.getTopicScope().name().toLowerCase(),
            event.getTenant(),
            event.getObjectType());
    return atroposPublisherClient
        .publish(event)
        .thenAccept(
            aVoid -> logInfo(businessObservation, topic))
        .exceptionally(
            throwable -> {
              throwable = unwrapCompletionStateException(throwable);
              logError(throwable, businessObservation);
              throw new BusinessExceptionInstancePublishingException(throwable);
            });
  }

  @Override
  public CompletionStage<Void> resolve(BusinessObservation businessObservation) {
    PubSubEvent.Builder event = constructPubSubEventBuilder(BusinessObservationEvent.from(businessObservation),closeObjectType);
    String topic =
        String.format(
            TOPIC_NAME_FORMAT,
            event.getTopicScope().name().toLowerCase(),
            event.getTenant(),
            event.getObjectType());
    return atroposPublisherClient
        .publish(event)
        .thenAccept(
            aVoid -> logInfo(businessObservation, topic))
        .exceptionally(
            throwable -> {
              throwable = unwrapCompletionStateException(throwable);
              logError(throwable, businessObservation);
              throw new BusinessExceptionInstancePublishingException(throwable);
            });
  }

  private PubSubEvent.Builder constructPubSubEventBuilder(
      BusinessObservationEvent businessObservationEvent,String objectType) {

    try {
      PubSubEvent.Builder event = new PubSubEvent.Builder();
      NameValuePair[] sourceAttributes = {
          new BasicNameValuePair("app", Publisher.getAppNameFromProperty()),
          new BasicNameValuePair("cluster", Publisher.getClusterNameFromProperty())
      };
      Tag tag =
          Tag.builder()
              .tagType("definitionCode")
              .tagValue(businessObservationEvent.getDefinitionCode())
              .attributes(sourceAttributes)
              .build();

      event
          .data(gson.toJsonTree(businessObservationEvent))
          .tenant(String.valueOf(businessObservationEvent.getTenantId()))
          .tags(Arrays.asList(tag))
          .objectType(objectType)
          .operationType(OperationType.CREATED)
          .objectID(getObjectId(businessObservationEvent))
          .stateMachineState("NONE")
          .topicScope(TopicScope.TENANT)
          .sourceAttributes(sourceAttributes);
      return event;
    } catch (Throwable throwable) {
      Throwable unwrapCompletionStateException = unwrapCompletionStateException(throwable);
      logError(unwrapCompletionStateException, businessObservationEvent);
      throw new BusinessExceptionInstancePublishingException(unwrapCompletionStateException);
    }
  }

  private String getObjectId(BusinessObservationEvent event) {
    return event.getTenantId() + "." + event.getDefinitionCode();
  }

  private void logError(Throwable throwable, BusinessObservationEvent event) {
    logger.error("Publish event error", throwable)
        .attr("exceptionCode", event.getObservationInfo())
        .attr("tenantId", event.getTenantId())
        .attr("idempotentKey", event.getIdempotentKey())
        .log();
  }
  private void logError(Throwable throwable, BusinessObservation event) {
    logger.error("Publish event error", throwable)
        .attr("exceptionCode", event.getObservationInfo())
        .attr("tenantId", event.getTenantId())
        .attr("idempotentKey", event.getIdempotentKey())
        .log();
  }

  private void logInfo(BusinessObservation event, String topic) {
    logger.info("Successfully Exception Published")
        .attr("exceptionCode", event.getObservationInfo())
        .attr("topic", topic)
        .attr("tenantId", event.getTenantId())
        .attr("idempotentKey", event.getIdempotentKey())
        .log();
  }
}
