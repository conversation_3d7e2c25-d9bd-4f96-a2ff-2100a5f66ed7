package in.zeta.cronus.client.base;

import in.zeta.commons.concurrency.CompletableFutures;
import in.zeta.cronus.client.models.BusinessException;
import in.zeta.cronus.client.models.exception.BusinessExceptionValidationException;
import in.zeta.spectra.capture.SpectraLogger;
import olympus.trace.OlympusSpectra;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;

public class BusinessExceptionValidator {
  private final SpectraLogger logger = OlympusSpectra.getLogger(BusinessExceptionValidator.class);

  public CompletionStage<Void> validate(BusinessException businessExceptionInstance) {
    try {
      return CompletableFuture.completedFuture(
          validateExceptionCode(businessExceptionInstance.getBusinessExceptionDefinitionCode()));
    } catch (Throwable throwable) {
      Throwable exception = CompletableFutures.unwrapCompletionStateException(throwable);
      logger
          .error(exception.getMessage(), exception)
          .attr("tenantId", businessExceptionInstance.getTenantId())
          .attr(
              "exceptionDefinitionCode",
              businessExceptionInstance.getBusinessExceptionDefinitionCode())
          .log();
      throw new BusinessExceptionValidationException(exception.getMessage(), exception);
    }
  }

  public static Void validateExceptionCode(String exceptionCode) {
    String[] code = exceptionCode.split("\\.");
    if (code.length == 3) {
      return null;
    }
    throw new BusinessExceptionValidationException(
        "Invalid Exception Code", new Throwable("Invalid Exception Code"));
  }
}
