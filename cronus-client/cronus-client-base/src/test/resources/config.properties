#Crux token generation credentials(available on your service's vault after running elenchos pipeline for respective tenants)

crux-llt.god.oauth.app-auth-profile-id=
crux-llt.god.oauth.app-private-key=
crux-llt.god.oauth.client-id=
crux-llt.god.oauth.client-secret=
crux-llt.god.oauth.domain-id=0-admin.India
crux-llt.god.oauth.scope=admin

#SSO base URL(get it from crux/cipher doc)
sso.base.url=https://sso-pp.zetaapps.in

#Delta endpoint and secret(available in vault under zone properties of delta)
delta.v2.url=https://elenchos.internal.mum1-pp.zetaapps.in/delta/api/v2/
delta.v2.encryption.base64Secret=

# Schaas base url
schaas.base.url=https://appinfra.internal.mum1-pp.zetaapps.in/schaas/api/v1/

#This value need not change
crux-llt.token.buffer.time=2700000

app.name=cronus-client
cluster.name=rhea

proteus.endpoint=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
certstore.proteus.endpoint=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
sessions.proteus.endpoint=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
