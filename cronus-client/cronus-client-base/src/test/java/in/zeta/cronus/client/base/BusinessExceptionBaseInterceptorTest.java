package in.zeta.cronus.client.base;

import in.zeta.cronus.client.models.BusinessException;
import in.zeta.cronus.client.models.CloseException;
import in.zeta.cronus.client.models.CronusBusinessException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.CompletionStage;
import org.aopalliance.intercept.MethodInvocation;
import org.aspectj.lang.ProceedingJoinPoint;
import org.junit.Assert;
import org.junit.Test;
import org.mockito.Mockito;

public class BusinessExceptionBaseInterceptorTest {

  public static class MockBusinessExceptionHandler implements BusinessExceptionHandler {

    public static final Map<String, Object> mockPublish = new HashMap<>();

    public static void reset() {
      mockPublish.clear();
    }

    @Override
    public CompletionStage<Void> publish(BusinessException BusinessExceptionInstance) {
      mockPublish.put(BusinessExceptionInstance.getIdempotentKey(), BusinessExceptionInstance);
      return CompletableFuture.runAsync(() -> {
      });
    }
    @Override
    public CompletionStage<Void> close(CloseException closeException) {
      mockPublish.put(closeException.getIdempotentKey(), closeException);
      return CompletableFuture.runAsync(() -> {
      });
    }
  }



  @Test
  public void test_interceptSimpleMethod() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
    MockBusinessExceptionHandler.reset();
    ProceedingJoinPoint proceedingJoinPoint = Mockito.mock(ProceedingJoinPoint.class);
    Mockito.when(proceedingJoinPoint.proceed()).thenThrow(
        CronusBusinessException.getInstance("12345","abcd.com.12345"
    ,"case1",new HashMap<>()));
    try {
      businessExceptionBaseInterceptor.interceptMethod(proceedingJoinPoint);
    } catch (Throwable throwable) {
      assert throwable instanceof CronusBusinessException;
    }
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.containsKey("case1"));
  }


  @Test
  public void test_interceptSimpleMethod_methodInvocation() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
    MockBusinessExceptionHandler.reset();
    MethodInvocation proceedingJoinPoint = Mockito.mock(MethodInvocation.class);
    Mockito.when(proceedingJoinPoint.proceed()).thenThrow(
        CronusBusinessException.getInstance("12345","abcd.com.12345"
        ,"case2",new HashMap<>()));
    try {
      businessExceptionBaseInterceptor.interceptMethod(proceedingJoinPoint);
    } catch (Throwable throwable) {
      assert throwable instanceof CronusBusinessException;
    }
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.containsKey("case2"));
  }

  @Test
  public void test_interceptFutureMethod_methodInvocation() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
    MockBusinessExceptionHandler.reset();
    MethodInvocation proceedingJoinPoint = Mockito.mock(MethodInvocation.class);
    CompletableFuture<String> future = new CompletableFuture();
    future.completeExceptionally(CronusBusinessException.getInstance("12345","abcd.com.12345"
        ,"case3",new HashMap<>()));
    Mockito.when(proceedingJoinPoint.proceed()).thenReturn(future);
    try {
       businessExceptionBaseInterceptor.interceptFutureMethod(proceedingJoinPoint);
    } catch (Throwable throwable) {
      assert throwable instanceof CronusBusinessException;
    }
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.containsKey("case3"));
  }

  @Test
  public void test_interceptFutureMethod() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
    MockBusinessExceptionHandler.reset();
    ProceedingJoinPoint proceedingJoinPoint = Mockito.mock(ProceedingJoinPoint.class);
    CompletableFuture<String> future = new CompletableFuture();
    future.completeExceptionally(CronusBusinessException.getInstance("12345","abcd.com.12345"
        ,"case3",new HashMap<>()));
    Mockito.when(proceedingJoinPoint.proceed()).thenReturn(future);
    try {
      businessExceptionBaseInterceptor.interceptFutureMethod(proceedingJoinPoint);
    } catch (Throwable throwable) {
      assert throwable instanceof CronusBusinessException;
    }
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.containsKey("case3"));
  }
  @Test
  public void test_interceptSimpleMethod_nonBusinessException() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
    MockBusinessExceptionHandler.reset();
    ProceedingJoinPoint proceedingJoinPoint = Mockito.mock(ProceedingJoinPoint.class);
    Mockito.when(proceedingJoinPoint.proceed()).thenThrow(new RuntimeException("yes"));
    try {
      businessExceptionBaseInterceptor.interceptMethod(proceedingJoinPoint);
    } catch (Throwable throwable) {
      assert throwable instanceof RuntimeException;
    }
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.isEmpty());
  }

  @Test
  public void test_interceptSimpleMethod_no_exception() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
    MockBusinessExceptionHandler.reset();
    ProceedingJoinPoint proceedingJoinPoint = Mockito.mock(ProceedingJoinPoint.class);
    Mockito.when(proceedingJoinPoint.proceed()).thenReturn("ok");
    businessExceptionBaseInterceptor.interceptMethod(proceedingJoinPoint);
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.isEmpty());
  }

  @Test
  public void test_interceptSimpleMethod_no_exception_methodInvocation() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(new MockBusinessExceptionHandler());
    MockBusinessExceptionHandler.reset();
    MethodInvocation proceedingJoinPoint = Mockito.mock(MethodInvocation.class);
    Mockito.when(proceedingJoinPoint.proceed()).thenReturn("ok");
    businessExceptionBaseInterceptor.interceptMethod(proceedingJoinPoint);
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.isEmpty());
  }

  @Test
  public void test_interceptSimpleMethod_issue_in_publish() throws Throwable {
    BusinessExceptionBaseInterceptor businessExceptionBaseInterceptor = new BusinessExceptionBaseInterceptor(null);
    MockBusinessExceptionHandler.reset();
    MethodInvocation proceedingJoinPoint = Mockito.mock(MethodInvocation.class);
    Mockito.when(proceedingJoinPoint.proceed()).thenThrow(
        CronusBusinessException.getInstance("12345","abcd.com.12345"
        ,"case7",new HashMap<>()));
    try {
      businessExceptionBaseInterceptor.interceptMethod(proceedingJoinPoint);
    } catch (Throwable throwable) {
      assert throwable instanceof CronusBusinessException;
    }
    Assert.assertTrue(MockBusinessExceptionHandler.mockPublish.isEmpty());
  }



}
