package in.zeta.cronus.client.base;

import in.zeta.cronus.client.base.publisher.AtroposBusinessExceptionInstanceClient;
import in.zeta.cronus.client.base.publisher.TestHelper;
import in.zeta.cronus.client.models.CronusBusinessException;
import in.zeta.cronus.client.models.Publisher;
import in.zeta.oms.atropos.client.AtroposPublisherClient;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;
import java.io.IOException;
import java.util.HashMap;
import static in.zeta.cronus.client.base.publisher.TestHelper.tenantId;

@RunWith(MockitoJUnitRunner.class)
public class BusinessExceptionHandlerTest {
    private static AbstractBusinessExceptionHandler businessExceptionInstanceClient;
    private static AtroposPublisherClient atroposPublisherClient;
    private static BusinessExceptionValidator businessExceptionValidator;
    private static CronusBusinessException cronusBusinessException;

    @BeforeClass()
    public static void setup() throws IOException {
        atroposPublisherClient = TestHelper.initializeAtroposPublisherClient(false);
        businessExceptionValidator = new BusinessExceptionValidator();
        businessExceptionInstanceClient =
                new AtroposBusinessExceptionInstanceClient(atroposPublisherClient, businessExceptionValidator);
    }

    @Test
    public void testGenerateIdempotencyKey() {
        String businessExceptionCode1 = "clearance.aura.BEDZZ1157";
        cronusBusinessException =  CronusBusinessException.getInstance(tenantId,TestHelper.businessExceptionCode,"1234",new HashMap<>());
        CronusBusinessException cronusBusinessException2 =  CronusBusinessException.getInstance("1000004",TestHelper.businessExceptionCode,"test:1234",new HashMap<>());
        CronusBusinessException cronusBusinessException3 =  CronusBusinessException.getInstance("1000004",businessExceptionCode1,null,new HashMap<>());
        CronusBusinessException cronusBusinessException4 =  CronusBusinessException.getInstance("1000004",TestHelper.businessExceptionCode,"123456",new HashMap<>());
        String key1 =  businessExceptionInstanceClient.generateIdempotencyKey(
            cronusBusinessException);
        String key2 =  businessExceptionInstanceClient.generateIdempotencyKey(
            cronusBusinessException);
        String key3 =  businessExceptionInstanceClient.generateIdempotencyKey(
            cronusBusinessException2);
        String key4 =  businessExceptionInstanceClient.generateIdempotencyKey(
            cronusBusinessException3);
        String key5 =  businessExceptionInstanceClient.generateIdempotencyKey(
            cronusBusinessException4);
        assert key1.equals(key2) && !key1.equals(key3) && !key2.equals(key4) && !key3.equals(key4) && !key5.equals(key3);
    }

    @Test
    public void testAppNamefromDefault(){
        String appName = Publisher.getAppNameFromProperty();
        assert appName.equals("unknown-service");
    }

    @Test
    public void testClusterNamefromDefault(){
        String appName = Publisher.getClusterNameFromProperty();
        assert appName.equals("default");
    }

    @Test
    public void testGetAppNameFromProperty(){
        System.setProperty("app.name", "test");
        String appName = Publisher.getAppNameFromProperty();
        System.clearProperty("app.name");
        assert appName.equals("test");
    }

    @Test
    public void testGetClusterNameFromProperty(){
        System.setProperty("cluster.name", "test");
        String appName = Publisher.getClusterNameFromProperty();
        System.clearProperty("cluster.name");
        assert appName.equals("test");
    }

}