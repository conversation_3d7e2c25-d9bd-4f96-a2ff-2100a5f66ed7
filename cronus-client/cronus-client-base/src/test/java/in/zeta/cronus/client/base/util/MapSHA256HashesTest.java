package in.zeta.cronus.client.base.util;

import java.util.HashMap;
import junit.framework.TestCase;
import org.junit.Assert;
import org.junit.Test;

public class MapSHA256HashesTest extends TestCase {


  @Test
  public void test_calculateSHA256Hash() {
    HashMap<String, Object> requestInfo = getHasMap("test");
    String hash1= MapSHA256Hashes.calculateSHA256Hash(requestInfo);
    requestInfo.clear();
    requestInfo = getHasMap("test");
    String hash2 = MapSHA256Hashes.calculateSHA256Hash(requestInfo);
    Assert.assertEquals(hash1, hash2);
  }

  @Test
  public void test_calculateSHA256Hash_out_of_order() {
    HashMap<String, Object> requestInfo = getHasMap("test");
    requestInfo.putAll(getHasMap("test2"));
    String hash1 = MapSHA256Hashes.calculateSHA256Hash(requestInfo);
    requestInfo.clear();
    requestInfo.putAll(getHasMap("test2"));
    requestInfo.putAll(getHasMap("test"));
    String hash2 = MapSHA256Hashes.calculateSHA256Hash(requestInfo);
    Assert.assertEquals(hash1, hash2);
  }


  private HashMap<String, Object> getHasMap(String key) {
    HashMap<String, Object> requestInfo = new HashMap<>();
    requestInfo.put(key, key);
    return requestInfo;
  }
}