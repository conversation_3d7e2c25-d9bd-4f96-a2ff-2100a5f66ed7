package in.zeta.cronus.client.base.publisher;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import in.zeta.oms.atropos.client.AtroposPublisherClient;
import olympus.common.JID;
import olympus.pubsub.model.PubSubEvent;
import org.mockito.Mockito;

import java.io.File;
import java.io.FileReader;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Properties;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.CompletableFuture;

public class TestHelper {
    public static final String IDEMPOTENT_KEY = "test:12345";
    public static String tenantId = "1000005";
    public static String businessExceptionCode = "clearance.athena.BEDZZ1157";


    public static Properties getProperties(String path) throws IOException {
        Properties properties = new Properties();
        properties.load(new FileReader(new File(path)));
        return properties;
    }

    public static JsonObject generateEventPayload(Gson gson){
        String eventID = UUID.randomUUID().toString();
        String eventName = "_tenant_" + tenantId + "_Event";
        String accountId = UUID.randomUUID().toString();
        String name = "test";

        CustomAccountRequest customAccountRequest = CustomAccountRequest.builder()
                .accountId(accountId)
                .accountHolderName(name)
                .build() ;

        EventPayload dummyEvent = EventPayload.builder()
                .eventId(eventID)
                .eventName(eventName)
                .attributes(new HashMap<>())
                .data(customAccountRequest)
                .build();

        JsonObject eventPayload = gson.fromJson(gson.toJson(dummyEvent),JsonObject.class);
        return eventPayload;
    }

    public static JsonObject generateEventPayloadForIdempotencyCheck(Gson gson){
        String eventID = UUID.randomUUID().toString();
        String eventName = "_tenant_" + tenantId + "_Event";
        String accountId = UUID.randomUUID().toString();
        String name = "testing";

        CustomAccountRequest customAccountRequest = CustomAccountRequest.builder()
                .accountId(accountId)
                .accountHolderName(name)
                .build() ;

        EventPayload dummyEvent = EventPayload.builder()
                .eventId(eventID)
                .eventName(eventName)
                .attributes(new HashMap<>())
                .data(customAccountRequest)
                .build();

        JsonObject eventPayload = gson.fromJson(gson.toJson(dummyEvent),JsonObject.class);
        return eventPayload;
    }

    public static AtroposPublisherClient initializeAtroposPublisherClient(boolean enableMock)
            throws IOException {

        AtroposPublisherClient atroposPublisherClient;
        if (enableMock) {
            atroposPublisherClient = Mockito.mock(AtroposPublisherClient.class);
            Mockito.when(atroposPublisherClient.publish(Mockito.any(PubSubEvent.Builder.class)))
                    .thenReturn(CompletableFuture.completedFuture(null));
        } else {
            Properties properties = getProperties("src/test/resources/config.properties");
            Random random = new Random();
            atroposPublisherClient = new AtroposPublisherClient(
                    new JID(random.nextInt(100000) + "@" + properties.getProperty("app.name") + ".services.olympus"));
        }

        return atroposPublisherClient;

    }

}
