package in.zeta.cronus.client.base.publisher;

import com.google.gson.Gson;
import in.zeta.cronus.client.base.AbstractBusinessExceptionHandler;
import in.zeta.cronus.client.base.BusinessExceptionValidator;
import in.zeta.cronus.client.models.CloseBusinessException;
import in.zeta.cronus.client.models.CronusBusinessException;
import in.zeta.cronus.client.models.ExceptionPublisherAction;
import in.zeta.oms.atropos.client.AtroposPublisherClient;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;

import static in.zeta.cronus.client.base.publisher.TestHelper.IDEMPOTENT_KEY;
import static in.zeta.cronus.client.base.publisher.TestHelper.tenantId;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class AtroposBusinessExceptionHandlerTest {


    private static AbstractBusinessExceptionHandler businessExceptionInstanceClient;
    private static CronusBusinessException cronusBusinessException;
    private static AtroposPublisherClient atroposPublisherClient;
    private static BusinessExceptionValidator businessExceptionValidator;
    private static CloseBusinessException closeBusinessException;

    @BeforeClass()
    public static void setup() throws IOException {
        cronusBusinessException = initializeBusinessExceptionInstance();
        atroposPublisherClient = TestHelper.initializeAtroposPublisherClient(false);
        businessExceptionValidator = new BusinessExceptionValidator();
        businessExceptionInstanceClient =
                new AtroposBusinessExceptionInstanceClient(atroposPublisherClient, businessExceptionValidator);
        closeBusinessException=initializeCloseBusinessExceptionInstance();
    }

    @Test
    public void testPublishBusinessExceptionInstance() throws InterruptedException, ExecutionException {
        CompletableFuture<Void> completed =
                businessExceptionInstanceClient.publish(cronusBusinessException).toCompletableFuture();
        assertTrue(completed.isDone());
    }

    @Test
    public void testCloseBusinessExceptionInstance() throws InterruptedException, ExecutionException {
        CompletableFuture<Void> completed =
                businessExceptionInstanceClient.close(closeBusinessException).toCompletableFuture();
        assertTrue(completed.isDone());
    }





    private static CronusBusinessException initializeBusinessExceptionInstance() {
        cronusBusinessException =  CronusBusinessException.getInstance(tenantId,TestHelper.businessExceptionCode, IDEMPOTENT_KEY,new HashMap<>());
        return cronusBusinessException;
    }

    private static CloseBusinessException initializeCloseBusinessExceptionInstance() {
        closeBusinessException =  CloseBusinessException.getInstance(tenantId,TestHelper.businessExceptionCode, IDEMPOTENT_KEY, ExceptionPublisherAction.CLOSE,"close by system");
        return closeBusinessException;
    }


}