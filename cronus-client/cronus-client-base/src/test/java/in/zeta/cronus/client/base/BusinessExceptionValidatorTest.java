package in.zeta.cronus.client.base;

import com.google.gson.Gson;
import in.zeta.cronus.client.base.publisher.TestHelper;
import in.zeta.cronus.client.models.CronusBusinessException;
import in.zeta.cronus.client.models.exception.BusinessExceptionValidationException;
import org.junit.BeforeClass;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.runners.MockitoJUnitRunner;

import java.io.IOException;
import java.util.HashMap;
import java.util.concurrent.CompletableFuture;

import static in.zeta.cronus.client.base.publisher.TestHelper.IDEMPOTENT_KEY;
import static in.zeta.cronus.client.base.publisher.TestHelper.tenantId;
import static org.junit.Assert.assertTrue;

@RunWith(MockitoJUnitRunner.class)
public class BusinessExceptionValidatorTest {

    private static BusinessExceptionValidator businessExceptionValidator;
    private static CronusBusinessException cronusBusinessException;
    private static Gson gson;


    @BeforeClass()
    public static void setup() throws IOException {
        businessExceptionValidator = new BusinessExceptionValidator();
        gson = new Gson();
        initializeBusinessExceptionInstance();
    }

    @Test
    public void testValidateFunction(){
        CompletableFuture<Void> completed = businessExceptionValidator.validate(
            cronusBusinessException).toCompletableFuture();
        assertTrue(completed.isDone());
    }

    @Test(expected = BusinessExceptionValidationException.class)
    public void testFailedValidation(){
        CompletableFuture<Void> completed = businessExceptionValidator.validate(initializeBusinessExceptionInstanceFaulty()).toCompletableFuture();
        assertTrue(completed.isDone());
    }

    private static CronusBusinessException initializeBusinessExceptionInstanceFaulty() {
        String businessExceptionCode = "clearance.athena";
        return CronusBusinessException.builder()
            .exceptionInfo(new HashMap<>())
            .tenantId(tenantId)
            .idempotentKey(IDEMPOTENT_KEY)
            .businessExceptionDefinitionCode(businessExceptionCode)
            .build();
    }

    private static CronusBusinessException initializeBusinessExceptionInstance() {
        cronusBusinessException =  CronusBusinessException.getInstance(tenantId,TestHelper.businessExceptionCode,
            IDEMPOTENT_KEY,new HashMap<>());
        return cronusBusinessException;
    }
}