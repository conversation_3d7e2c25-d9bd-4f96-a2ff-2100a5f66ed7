# cronus-client Changelog
Changelog of cronus-client.

## v1.0.9 (2023-10-17)

**[d6dffb25b270edb](https://bitbucket.org/zetaengg/cronus-client/commits/d6dffb25b270edb) Merged in TCH-17552 (pull request #4)**

* TCH-17552
* TCH-15480 | fix release issues |
* TCH-17552 | refactoring |
* TCH-17552 | refactoring |
* Approved-by: <PERSON>

Author - Subodh Rai *2023-10-17 16:09:24*

## v1.0.8 (2023-10-04)

**[73be4e65a47abc6](https://bitbucket.org/zetaengg/cronus-client/commits/73be4e65a47abc6) Merged in fix_release_issues (pull request #3)**

* TCH-15480 | fix release issues |
* TCH-15480 | fix release issues |
* TCH-15480 | fix release issues |
* TCH-15480 | fix release issues |
* TCH-15480 | fix release issues |
* Approved-by: <PERSON>

Author - <PERSON><PERSON><PERSON> *2023-10-04 09:16:22*

**[7f5cb0addfd7af8](https://bitbucket.org/zetaengg/cronus-client/commits/7f5cb0addfd7af8) Merged in provide_interceptor (pull request #2)**

* TCH-17058 | Cronus library Changes
* TCH-15480 | refactoring | added bean for base interceptor
* TCH-15480 | refactoring | added dependencies
* TCH-15480 | refactoring | added testCases
* TCH-15480 | refactoring | added testCases
* TCH-15480 | refactoring | added testCases
* TCH-15480 | refactoring | added testCases
* TCH-15480 | refactoring | added testCases
* TCH-15480 | refactoring | added testCases
* TCH-15480 | refactoring | added testCases
* TCH-15480 | refactoring | added testCases
* Approved-by: Jai Gupta

Author - Subodh Rai *2023-10-03 16:51:06*

**[60e05f9e2635ce8](https://bitbucket.org/zetaengg/cronus-client/commits/60e05f9e2635ce8) TCH-15480 Refactored Library**


Author - sanidhya samadhiya *2023-09-24 09:25:28*

**[7a56c664aa73b05](https://bitbucket.org/zetaengg/cronus-client/commits/7a56c664aa73b05) TCH-15480 New Versions**


Author - sanidhya samadhiya *2023-09-22 08:36:32*

**[4eed2d67c53e6e4](https://bitbucket.org/zetaengg/cronus-client/commits/4eed2d67c53e6e4) TCH-15480 Refactored Operator added new names**


Author - sanidhya samadhiya *2023-09-20 12:31:22*

## v1.0.2 (2023-09-18)

**[611aca9e68b797a](https://bitbucket.org/zetaengg/cronus-client/commits/611aca9e68b797a) TCH-15480 Added Cronu Client lib for Release**


Author - sanidhya samadhiya *2023-09-18 10:38:28*

**[676b4a78c361c99](https://bitbucket.org/zetaengg/cronus-client/commits/676b4a78c361c99) TCH-15480 Added Cronu Client lib for Release**


Author - sanidhya samadhiya *2023-09-18 10:37:13*

**[d316b33f328f541](https://bitbucket.org/zetaengg/cronus-client/commits/d316b33f328f541) TCH-15480 Added Cronu Client lib**


Author - sanidhya samadhiya *2023-09-16 14:16:05*

**[b613dc130b6ce88](https://bitbucket.org/zetaengg/cronus-client/commits/b613dc130b6ce88) TCH-15480 Added Cronu Client lib**


Author - sanidhya samadhiya *2023-09-16 14:13:09*

**[416d9c6a64e71d0](https://bitbucket.org/zetaengg/cronus-client/commits/416d9c6a64e71d0) TCH-15480 Added Cronus Client**


Author - sanidhya samadhiya *2023-09-14 08:50:25*

