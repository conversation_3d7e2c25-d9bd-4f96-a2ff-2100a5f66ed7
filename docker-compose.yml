version: '3.8'

services:
  cronus-service:
    build:
      context: .
      dockerfile: packages/cronus-service/Dockerfile
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - PORT=3000
      - LOG_LEVEL=info
      - APP_NAME=cronus-service
      - CLUSTER_NAME=rhea
      - SSO_BASE_URL=https://sso-pp.zetaapps.in
      - CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID=${CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID}
      - CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY=${CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY}
      - CRUX_LLT_GOD_OAUTH_CLIENT_ID=${CRUX_LLT_GOD_OAUTH_CLIENT_ID}
      - CRUX_LLT_GOD_OAUTH_CLIENT_SECRET=${CRUX_LLT_GOD_OAUTH_CLIENT_SECRET}
      - CRUX_LLT_GOD_OAUTH_DOMAIN_ID=0-admin.India
      - CRUX_LLT_GOD_OAUTH_SCOPE=admin
      - CRUX_LLT_TOKEN_BUFFER_TIME=2700000
      - DELTA_V2_URL=https://elenchos.internal.mum1-pp.zetaapps.in/delta/api/v2/
      - DELTA_V2_ENCRYPTION_BASE64_SECRET=${DELTA_V2_ENCRYPTION_BASE64_SECRET}
      - SCHAAS_BASE_URL=https://appinfra.internal.mum1-pp.zetaapps.in/schaas/api/v1/
      - PROTEUS_ENDPOINT=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
      - CERTSTORE_PROTEUS_ENDPOINT=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
      - SESSIONS_PROTEUS_ENDPOINT=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
      - RATE_LIMIT_WINDOW_MS=900000
      - RATE_LIMIT_MAX_REQUESTS=100
      - CORS_ORIGIN=*
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    networks:
      - cronus-network

networks:
  cronus-network:
    driver: bridge
