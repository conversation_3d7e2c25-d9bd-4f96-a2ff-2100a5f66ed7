# Cronus Client JavaScript Library & Node.js Service

A production-grade JavaScript/TypeScript implementation of the Cronus Client library for handling business exceptions and observations in distributed systems, with automatic publishing to Atropos message queue.

## 🏗️ Architecture

This project consists of two main components:

1. **@zeta/cronus-client** - A TypeScript library for business exception handling
2. **cronus-service** - A Node.js REST API service that demonstrates usage of the library

## 📦 Project Structure

```
├── packages/
│   ├── cronus-client/          # Core TypeScript library
│   │   ├── src/
│   │   │   ├── models/         # Data models and interfaces
│   │   │   ├── core/           # Core business logic
│   │   │   ├── decorators/     # TypeScript decorators
│   │   │   ├── config/         # Configuration management
│   │   │   └── utils/          # Utility classes
│   │   └── __tests__/          # Unit tests
│   └── cronus-service/         # Node.js REST service
│       ├── src/
│       │   ├── controllers/    # REST API controllers
│       │   ├── services/       # Business services
│       │   ├── middleware/     # Express middleware
│       │   └── config/         # Configuration
│       └── __tests__/          # Integration tests
├── docker-compose.yml          # Docker composition
└── README.md                   # This file
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ 
- npm 9+
- Docker (optional)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cronus-client-js
   ```

2. **Install dependencies**
   ```bash
   npm install
   ```

3. **Build the library**
   ```bash
   npm run build
   ```

4. **Set up environment variables**
   ```bash
   cp packages/cronus-service/.env.example packages/cronus-service/.env
   # Edit .env file with your configuration
   ```

5. **Start the service**
   ```bash
   cd packages/cronus-service
   npm run start:dev
   ```

## 🔧 Configuration

### Environment Variables

The service requires the following environment variables:

#### Required
- `APP_NAME` - Application name
- `CLUSTER_NAME` - Cluster name
- `SSO_BASE_URL` - SSO service base URL
- `CRUX_LLT_GOD_OAUTH_*` - OAuth configuration
- `DELTA_V2_URL` - Delta service URL
- `DELTA_V2_ENCRYPTION_BASE64_SECRET` - Delta encryption secret
- `SCHAAS_BASE_URL` - Schaas service URL
- `PROTEUS_ENDPOINT` - Proteus service endpoint
- `CERTSTORE_PROTEUS_ENDPOINT` - Certstore Proteus endpoint
- `SESSIONS_PROTEUS_ENDPOINT` - Sessions Proteus endpoint

#### Optional
- `PORT` - Server port (default: 3000)
- `NODE_ENV` - Environment (default: development)
- `LOG_LEVEL` - Logging level (default: info)
- `RATE_LIMIT_WINDOW_MS` - Rate limit window (default: 900000)
- `RATE_LIMIT_MAX_REQUESTS` - Max requests per window (default: 100)

## 📚 Library Usage

### Basic Usage

```typescript
import { 
  CronusClient, 
  CronusConfigurationLoader,
  CronusBusinessException,
  ExceptionPublisherAction 
} from '@zeta/cronus-client';

// Load configuration from environment
const config = CronusConfigurationLoader.fromEnvironment();

// Create client
const cronusClient = CronusClient.create(config);

// Publish a business exception
const exception = CronusBusinessException.builder()
  .setTenantId('tenant-123')
  .setBusinessExceptionDefinitionCode('PAYMENT_FAILED')
  .setIdempotentKey('payment-123')
  .setExceptionInfo({ amount: 100, currency: 'USD' })
  .setMessage('Payment processing failed')
  .build();

await cronusClient.getBusinessExceptionHandler().publish(exception);
```

### Using Decorators

```typescript
import { 
  PublishBusinessException, 
  BusinessExceptionEnabled,
  CronusBusinessException 
} from '@zeta/cronus-client';

@BusinessExceptionEnabled(cronusClient.getBusinessExceptionHandler())
class PaymentService {
  
  @PublishBusinessException
  async processPayment(amount: number): Promise<void> {
    if (amount <= 0) {
      throw CronusBusinessException.builder()
        .setTenantId('tenant-123')
        .setBusinessExceptionDefinitionCode('INVALID_AMOUNT')
        .setIdempotentKey(`payment-${Date.now()}`)
        .setExceptionInfo({ amount })
        .build();
    }
    
    // Payment processing logic
  }
}
```

## 🌐 REST API

### Endpoints

#### Health Checks
- `GET /health` - Basic health check
- `GET /health/readiness` - Readiness probe
- `GET /health/liveness` - Liveness probe
- `GET /status` - Detailed status information

#### Business Exceptions
- `POST /api/v1/exceptions` - Publish business exception
- `POST /api/v1/exceptions/close` - Close business exception

#### Business Observations
- `POST /api/v1/observations` - Publish business observation
- `POST /api/v1/observations/resolve` - Resolve business observation

### API Examples

#### Publish Business Exception
```bash
curl -X POST http://localhost:3000/api/v1/exceptions \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "tenant-123",
    "definitionCode": "PAYMENT_FAILED",
    "exceptionInfo": {
      "amount": 100,
      "currency": "USD",
      "orderId": "order-456"
    },
    "message": "Payment processing failed"
  }'
```

#### Close Business Exception
```bash
curl -X POST http://localhost:3000/api/v1/exceptions/close \
  -H "Content-Type: application/json" \
  -d '{
    "tenantId": "tenant-123",
    "definitionCode": "PAYMENT_FAILED",
    "idempotentKey": "payment-123",
    "action": "RESOLVE",
    "closureRemark": "Issue resolved by customer service"
  }'
```

## 🐳 Docker Deployment

### Using Docker Compose

```bash
# Set environment variables
export CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID="your-profile-id"
export CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY="your-private-key"
export CRUX_LLT_GOD_OAUTH_CLIENT_ID="your-client-id"
export CRUX_LLT_GOD_OAUTH_CLIENT_SECRET="your-client-secret"
export DELTA_V2_ENCRYPTION_BASE64_SECRET="your-delta-secret"

# Start the service
docker-compose up -d
```

### Building Docker Image

```bash
cd packages/cronus-service
docker build -t cronus-service .
docker run -p 3000:3000 --env-file .env cronus-service
```

## 🧪 Testing

### Run All Tests
```bash
npm test
```

### Run Library Tests
```bash
cd packages/cronus-client
npm test
```

### Run Service Tests
```bash
cd packages/cronus-service
npm test
```

### Coverage Reports
```bash
npm run test:coverage
```

## 🔍 Monitoring & Observability

### Health Checks
The service provides comprehensive health checks:
- **Liveness**: `/health/liveness` - Basic service availability
- **Readiness**: `/health/readiness` - Service ready to accept traffic
- **Health**: `/health` - Overall health status
- **Status**: `/status` - Detailed service information

### Logging
- Structured JSON logging using Winston
- Configurable log levels
- Request/response logging
- Error tracking with stack traces

### Metrics
- HTTP request metrics
- Response time tracking
- Error rate monitoring
- Memory usage tracking

## 🔒 Security Features

- **Helmet.js** - Security headers
- **Rate limiting** - Configurable request throttling
- **CORS** - Cross-origin resource sharing
- **Input validation** - Request payload validation
- **Non-root Docker user** - Container security

## 🚀 Production Considerations

### Performance
- Compression middleware for response optimization
- Connection pooling for HTTP clients
- Async/await patterns for non-blocking operations
- Memory-efficient JSON parsing

### Reliability
- Graceful shutdown handling
- Circuit breaker patterns (can be added)
- Retry mechanisms with exponential backoff
- Health check endpoints for load balancers

### Scalability
- Stateless design
- Horizontal scaling support
- Load balancer friendly
- Container orchestration ready

## 📄 License

MIT License - see LICENSE file for details

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📞 Support

For issues and questions:
- Create an issue in the repository
- Contact the development team
- Check the documentation

---

**Built with ❤️ by Zeta Enterprise**
