{"name": "cronus-client-workspace", "version": "1.0.0", "description": "Cronus Client JavaScript Library and Node.js Service Workspace", "private": true, "workspaces": ["packages/cronus-client", "packages/cronus-service"], "scripts": {"build": "npm run build --workspaces", "test": "npm run test --workspaces", "lint": "eslint packages/*/src/**/*.ts", "clean": "npm run clean --workspaces"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}