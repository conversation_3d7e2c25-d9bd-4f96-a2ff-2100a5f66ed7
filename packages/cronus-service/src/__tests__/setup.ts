import 'reflect-metadata';

// Global test setup
beforeEach(() => {
  // Clear environment variables that might affect tests
  delete process.env.PORT;
  delete process.env.NODE_ENV;
  delete process.env.LOG_LEVEL;
  
  // Set required environment variables for tests
  process.env.APP_NAME = 'test-app';
  process.env.CLUSTER_NAME = 'test-cluster';
  process.env.SSO_BASE_URL = 'https://sso.test.com';
  process.env.CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID = 'test-profile';
  process.env.CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY = 'test-key';
  process.env.CRUX_LLT_GOD_OAUTH_CLIENT_ID = 'test-client';
  process.env.CRUX_LLT_GOD_OAUTH_CLIENT_SECRET = 'test-secret';
  process.env.DELTA_V2_URL = 'https://delta.test.com';
  process.env.DELTA_V2_ENCRYPTION_BASE64_SECRET = 'test-secret';
  process.env.SCHAAS_BASE_URL = 'https://schaas.test.com';
  process.env.PROTEUS_ENDPOINT = 'https://proteus.test.com';
  process.env.CERTSTORE_PROTEUS_ENDPOINT = 'https://certstore.test.com';
  process.env.SESSIONS_PROTEUS_ENDPOINT = 'https://sessions.test.com';
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
