import request from 'supertest';
import express from 'express';
import { ExceptionController } from '../../controllers/ExceptionController';
import { CronusService } from '../../services/CronusService';
import { ExceptionPublisherAction } from '@zeta/cronus-client';
import { loadEnvironmentConfig } from '../../config/environment';

// Mock the CronusService
jest.mock('../../services/CronusService');
const MockedCronusService = CronusService as jest.MockedClass<typeof CronusService>;

describe('ExceptionController', () => {
  let app: express.Application;
  let mockCronusService: jest.Mocked<CronusService>;
  let controller: ExceptionController;

  beforeEach(() => {
    // Create mock service
    mockCronusService = {
      publishBusinessException: jest.fn(),
      closeBusinessException: jest.fn(),
      publishBusinessObservation: jest.fn(),
      resolveBusinessObservation: jest.fn(),
      getCronusClient: jest.fn(),
      healthCheck: jest.fn()
    } as any;

    MockedCronusService.mockImplementation(() => mockCronusService);

    // Create controller
    controller = new ExceptionController(mockCronusService);

    // Setup Express app
    app = express();
    app.use(express.json());
    
    // Setup routes
    app.post('/exceptions', 
      ExceptionController.publishValidationRules(),
      controller.publishException.bind(controller)
    );
    
    app.post('/exceptions/close',
      ExceptionController.closeValidationRules(),
      controller.closeException.bind(controller)
    );
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('POST /exceptions', () => {
    const validPayload = {
      tenantId: 'test-tenant',
      definitionCode: 'TEST_EXCEPTION',
      exceptionInfo: { field: 'value' },
      message: 'Test exception message'
    };

    it('should publish exception successfully', async () => {
      mockCronusService.publishBusinessException.mockResolvedValue();

      const response = await request(app)
        .post('/exceptions')
        .send(validPayload)
        .expect(201);

      expect(response.body).toEqual({
        success: true,
        message: 'Business exception published successfully',
        data: {
          tenantId: validPayload.tenantId,
          definitionCode: validPayload.definitionCode,
          timestamp: expect.any(String)
        }
      });

      expect(mockCronusService.publishBusinessException).toHaveBeenCalledWith(
        validPayload.tenantId,
        validPayload.definitionCode,
        validPayload.exceptionInfo,
        validPayload.message
      );
    });

    it('should return 400 for invalid tenantId', async () => {
      const invalidPayload = {
        ...validPayload,
        tenantId: 'invalid@tenant'
      };

      const response = await request(app)
        .post('/exceptions')
        .send(invalidPayload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.message).toBe('Validation failed');
      expect(response.body.errors).toHaveLength(1);
      expect(response.body.errors[0].path).toBe('tenantId');
    });

    it('should return 400 for missing tenantId', async () => {
      const invalidPayload = {
        ...validPayload,
        tenantId: ''
      };

      const response = await request(app)
        .post('/exceptions')
        .send(invalidPayload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors[0].path).toBe('tenantId');
    });

    it('should return 400 for invalid definitionCode', async () => {
      const invalidPayload = {
        ...validPayload,
        definitionCode: 'INVALID@CODE'
      };

      const response = await request(app)
        .post('/exceptions')
        .send(invalidPayload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors[0].path).toBe('definitionCode');
    });

    it('should return 500 when service throws error', async () => {
      mockCronusService.publishBusinessException.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .post('/exceptions')
        .send(validPayload)
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        message: 'Failed to publish business exception',
        error: 'Service error'
      });
    });

    it('should accept payload without optional fields', async () => {
      const minimalPayload = {
        tenantId: 'test-tenant',
        definitionCode: 'TEST_EXCEPTION'
      };

      mockCronusService.publishBusinessException.mockResolvedValue();

      await request(app)
        .post('/exceptions')
        .send(minimalPayload)
        .expect(201);

      expect(mockCronusService.publishBusinessException).toHaveBeenCalledWith(
        minimalPayload.tenantId,
        minimalPayload.definitionCode,
        undefined,
        undefined
      );
    });
  });

  describe('POST /exceptions/close', () => {
    const validPayload = {
      tenantId: 'test-tenant',
      definitionCode: 'TEST_EXCEPTION',
      idempotentKey: 'test-key-123',
      action: ExceptionPublisherAction.RESOLVE,
      closureRemark: 'Test closure remark'
    };

    it('should close exception successfully', async () => {
      mockCronusService.closeBusinessException.mockResolvedValue();

      const response = await request(app)
        .post('/exceptions/close')
        .send(validPayload)
        .expect(200);

      expect(response.body).toEqual({
        success: true,
        message: 'Business exception closed successfully',
        data: {
          tenantId: validPayload.tenantId,
          definitionCode: validPayload.definitionCode,
          idempotentKey: validPayload.idempotentKey,
          action: validPayload.action,
          timestamp: expect.any(String)
        }
      });

      expect(mockCronusService.closeBusinessException).toHaveBeenCalledWith(
        validPayload.tenantId,
        validPayload.definitionCode,
        validPayload.idempotentKey,
        validPayload.action,
        validPayload.closureRemark
      );
    });

    it('should return 400 for invalid action', async () => {
      const invalidPayload = {
        ...validPayload,
        action: 'INVALID_ACTION'
      };

      const response = await request(app)
        .post('/exceptions/close')
        .send(invalidPayload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors[0].path).toBe('action');
    });

    it('should return 400 for missing required fields', async () => {
      const invalidPayload = {
        tenantId: 'test-tenant'
        // Missing other required fields
      };

      const response = await request(app)
        .post('/exceptions/close')
        .send(invalidPayload)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.errors.length).toBeGreaterThan(1);
    });

    it('should return 500 when service throws error', async () => {
      mockCronusService.closeBusinessException.mockRejectedValue(new Error('Service error'));

      const response = await request(app)
        .post('/exceptions/close')
        .send(validPayload)
        .expect(500);

      expect(response.body).toEqual({
        success: false,
        message: 'Failed to close business exception',
        error: 'Service error'
      });
    });
  });
});
