import dotenv from 'dotenv';

// Load environment variables from .env file
dotenv.config();

/**
 * Environment configuration interface
 */
export interface EnvironmentConfig {
  // Server configuration
  port: number;
  nodeEnv: string;
  
  // Cronus configuration
  appName: string;
  clusterName: string;
  
  // SSO configuration
  ssoBaseUrl: string;
  cruxLltGodOauthAppAuthProfileId: string;
  cruxLltGodOauthAppPrivateKey: string;
  cruxLltGodOauthClientId: string;
  cruxLltGodOauthClientSecret: string;
  cruxLltGodOauthDomainId: string;
  cruxLltGodOauthScope: string;
  cruxLltTokenBufferTime: number;
  
  // Delta configuration
  deltaV2Url: string;
  deltaV2EncryptionBase64Secret: string;
  
  // Schaas configuration
  schaasBaseUrl: string;
  
  // Proteus configuration
  proteusEndpoint: string;
  certstoreProteusEndpoint: string;
  sessionsProteusEndpoint: string;
  
  // Logging
  logLevel: string;
  
  // Rate limiting
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
}

/**
 * Load and validate environment configuration
 */
export function loadEnvironmentConfig(): EnvironmentConfig {
  const config: EnvironmentConfig = {
    // Server configuration
    port: parseInt(process.env.PORT || '3000', 10),
    nodeEnv: process.env.NODE_ENV || 'development',
    
    // Cronus configuration
    appName: getRequiredEnv('APP_NAME'),
    clusterName: getRequiredEnv('CLUSTER_NAME'),
    
    // SSO configuration
    ssoBaseUrl: getRequiredEnv('SSO_BASE_URL'),
    cruxLltGodOauthAppAuthProfileId: getRequiredEnv('CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID'),
    cruxLltGodOauthAppPrivateKey: getRequiredEnv('CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY'),
    cruxLltGodOauthClientId: getRequiredEnv('CRUX_LLT_GOD_OAUTH_CLIENT_ID'),
    cruxLltGodOauthClientSecret: getRequiredEnv('CRUX_LLT_GOD_OAUTH_CLIENT_SECRET'),
    cruxLltGodOauthDomainId: process.env.CRUX_LLT_GOD_OAUTH_DOMAIN_ID || '0-admin.India',
    cruxLltGodOauthScope: process.env.CRUX_LLT_GOD_OAUTH_SCOPE || 'admin',
    cruxLltTokenBufferTime: parseInt(process.env.CRUX_LLT_TOKEN_BUFFER_TIME || '2700000', 10),
    
    // Delta configuration
    deltaV2Url: getRequiredEnv('DELTA_V2_URL'),
    deltaV2EncryptionBase64Secret: getRequiredEnv('DELTA_V2_ENCRYPTION_BASE64_SECRET'),
    
    // Schaas configuration
    schaasBaseUrl: getRequiredEnv('SCHAAS_BASE_URL'),
    
    // Proteus configuration
    proteusEndpoint: getRequiredEnv('PROTEUS_ENDPOINT'),
    certstoreProteusEndpoint: getRequiredEnv('CERTSTORE_PROTEUS_ENDPOINT'),
    sessionsProteusEndpoint: getRequiredEnv('SESSIONS_PROTEUS_ENDPOINT'),
    
    // Logging
    logLevel: process.env.LOG_LEVEL || 'info',
    
    // Rate limiting
    rateLimitWindowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '900000', 10), // 15 minutes
    rateLimitMaxRequests: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100', 10)
  };

  validateConfig(config);
  return config;
}

function getRequiredEnv(key: string): string {
  const value = process.env[key];
  if (!value) {
    throw new Error(`Required environment variable ${key} is not set`);
  }
  return value;
}

function validateConfig(config: EnvironmentConfig): void {
  if (config.port < 1 || config.port > 65535) {
    throw new Error('PORT must be between 1 and 65535');
  }
  
  if (config.rateLimitWindowMs < 1000) {
    throw new Error('RATE_LIMIT_WINDOW_MS must be at least 1000ms');
  }
  
  if (config.rateLimitMaxRequests < 1) {
    throw new Error('RATE_LIMIT_MAX_REQUESTS must be at least 1');
  }
  
  if (config.cruxLltTokenBufferTime < 0) {
    throw new Error('CRUX_LLT_TOKEN_BUFFER_TIME must be non-negative');
  }
}
