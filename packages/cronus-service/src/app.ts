import express from 'express';
import helmet from 'helmet';
import cors from 'cors';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import { WinstonLogger } from '@zeta/cronus-client';
import { CronusService } from './services/CronusService';
import { createRoutes } from './routes';
import { errorHandler, notFoundHandler, requestLogger } from './middleware/errorHandler';
import { EnvironmentConfig } from './config/environment';

/**
 * Creates and configures the Express application
 */
export function createApp(envConfig: EnvironmentConfig): express.Application {
  const app = express();
  
  // Initialize logger
  const logger = new WinstonLogger({
    level: envConfig.logLevel
  });

  // Initialize Cronus service
  const cronusService = new CronusService(envConfig);

  // Security middleware
  app.use(helmet({
    contentSecurityPolicy: {
      directives: {
        defaultSrc: ["'self'"],
        styleSrc: ["'self'", "'unsafe-inline'"],
        scriptSrc: ["'self'"],
        imgSrc: ["'self'", "data:", "https:"]
      }
    }
  }));

  // CORS configuration
  app.use(cors({
    origin: process.env.CORS_ORIGIN || '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
    credentials: true
  }));

  // Rate limiting
  const limiter = rateLimit({
    windowMs: envConfig.rateLimitWindowMs,
    max: envConfig.rateLimitMaxRequests,
    message: {
      success: false,
      message: 'Too many requests from this IP, please try again later.',
      retryAfter: Math.ceil(envConfig.rateLimitWindowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false
  });
  app.use(limiter);

  // Compression middleware
  app.use(compression());

  // Body parsing middleware
  app.use(express.json({ limit: '10mb' }));
  app.use(express.urlencoded({ extended: true, limit: '10mb' }));

  // Request logging
  app.use(requestLogger(logger));

  // Trust proxy for accurate IP addresses
  app.set('trust proxy', 1);

  // Routes
  app.use('/', createRoutes(cronusService));

  // 404 handler
  app.use(notFoundHandler);

  // Global error handler
  app.use(errorHandler(logger));

  return app;
}

/**
 * Graceful shutdown handler
 */
export function setupGracefulShutdown(server: any, logger: WinstonLogger): void {
  const shutdown = (signal: string) => {
    logger.info(`Received ${signal}, starting graceful shutdown`);
    
    server.close((err: Error) => {
      if (err) {
        logger.error('Error during server shutdown', { error: err.message });
        process.exit(1);
      }
      
      logger.info('Server closed successfully');
      process.exit(0);
    });

    // Force shutdown after 30 seconds
    setTimeout(() => {
      logger.error('Forced shutdown after timeout');
      process.exit(1);
    }, 30000);
  };

  process.on('SIGTERM', () => shutdown('SIGTERM'));
  process.on('SIGINT', () => shutdown('SIGINT'));
  
  process.on('uncaughtException', (err) => {
    logger.error('Uncaught exception', { error: err.message, stack: err.stack });
    process.exit(1);
  });
  
  process.on('unhandledRejection', (reason, promise) => {
    logger.error('Unhandled rejection', { 
      reason: reason instanceof Error ? reason.message : String(reason),
      promise: promise.toString()
    });
    process.exit(1);
  });
}
