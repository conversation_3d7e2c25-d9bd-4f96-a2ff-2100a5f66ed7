import { Request, Response, NextFunction } from 'express';
import { <PERSON><PERSON><PERSON><PERSON> } from '@zeta/cronus-client';

/**
 * Global error handler middleware
 */
export function errorHandler(logger: <PERSON><PERSON>ogger) {
  return (error: Error, req: Request, res: Response, next: NextFunction): void => {
    // Log the error
    logger.error('Unhandled error in request', {
      error: error.message,
      stack: error.stack,
      method: req.method,
      url: req.url,
      headers: req.headers,
      body: req.body,
      params: req.params,
      query: req.query
    });

    // Don't expose internal errors in production
    const isDevelopment = process.env.NODE_ENV === 'development';
    
    res.status(500).json({
      success: false,
      message: 'Internal server error',
      error: isDevelopment ? error.message : 'Something went wrong',
      ...(isDevelopment && { stack: error.stack }),
      timestamp: new Date().toISOString()
    });
  };
}

/**
 * 404 handler middleware
 */
export function notFoundHandler(req: Request, res: Response): void {
  res.status(404).json({
    success: false,
    message: 'Endpoint not found',
    path: req.path,
    method: req.method,
    timestamp: new Date().toISOString()
  });
}

/**
 * Request logging middleware
 */
export function requestLogger(logger: WinstonLogger) {
  return (req: Request, res: Response, next: NextFunction): void => {
    const start = Date.now();
    
    res.on('finish', () => {
      const duration = Date.now() - start;
      
      logger.info('HTTP Request', {
        method: req.method,
        url: req.url,
        statusCode: res.statusCode,
        duration: `${duration}ms`,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        contentLength: res.get('Content-Length')
      });
    });
    
    next();
  };
}
