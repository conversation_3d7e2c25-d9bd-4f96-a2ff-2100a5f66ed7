import { Request, Response } from 'express';
import { CronusService } from '../services/CronusService';

/**
 * Controller for health check endpoints
 */
export class HealthController {
  constructor(private readonly cronusService: CronusService) {}

  /**
   * Basic health check endpoint
   */
  async healthCheck(req: Request, res: Response): Promise<void> {
    try {
      const health = await this.cronusService.healthCheck();
      res.status(200).json({
        success: true,
        ...health
      });
    } catch (error) {
      res.status(503).json({
        success: false,
        status: 'unhealthy',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Readiness probe endpoint
   */
  async readiness(req: Request, res: Response): Promise<void> {
    try {
      // Check if the service is ready to accept traffic
      const health = await this.cronusService.healthCheck();
      res.status(200).json({
        success: true,
        ready: true,
        ...health
      });
    } catch (error) {
      res.status(503).json({
        success: false,
        ready: false,
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Liveness probe endpoint
   */
  async liveness(req: Request, res: Response): Promise<void> {
    // Simple liveness check - if we can respond, we're alive
    res.status(200).json({
      success: true,
      alive: true,
      timestamp: new Date().toISOString()
    });
  }

  /**
   * Detailed status endpoint
   */
  async status(req: Request, res: Response): Promise<void> {
    try {
      const health = await this.cronusService.healthCheck();
      const cronusClient = this.cronusService.getCronusClient();
      
      res.status(200).json({
        success: true,
        service: {
          name: 'cronus-service',
          version: process.env.npm_package_version || '1.0.0',
          environment: process.env.NODE_ENV || 'development',
          uptime: process.uptime(),
          memory: process.memoryUsage(),
          pid: process.pid
        },
        cronus: {
          appName: cronusClient.getConfig().appName,
          clusterName: cronusClient.getConfig().clusterName,
          ssoBaseUrl: cronusClient.getConfig().sso.baseUrl
        },
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        error: error instanceof Error ? error.message : String(error),
        timestamp: new Date().toISOString()
      });
    }
  }
}
