import { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { CronusService } from '../services/CronusService';
import { ExceptionPublisherAction } from '@zeta/cronus-client';

/**
 * Controller for handling business exception endpoints
 */
export class ExceptionController {
  constructor(private readonly cronusService: CronusService) {}

  /**
   * Validation rules for publishing business exception
   */
  static publishValidationRules() {
    return [
      body('tenantId')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9.-]+$/)
        .withMessage('TenantId must be alphanumeric with dots and hyphens, max 100 characters'),
      body('definitionCode')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('DefinitionCode must be alphanumeric with underscores and hyphens, max 100 characters'),
      body('exceptionInfo')
        .optional()
        .isObject()
        .withMessage('ExceptionInfo must be an object'),
      body('message')
        .optional()
        .isString()
        .isLength({ max: 500 })
        .withMessage('Message must be a string with max 500 characters')
    ];
  }

  /**
   * Validation rules for closing business exception
   */
  static closeValidationRules() {
    return [
      body('tenantId')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9.-]+$/)
        .withMessage('TenantId must be alphanumeric with dots and hyphens, max 100 characters'),
      body('definitionCode')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('DefinitionCode must be alphanumeric with underscores and hyphens, max 100 characters'),
      body('idempotentKey')
        .isString()
        .isLength({ min: 1, max: 255 })
        .withMessage('IdempotentKey is required and must be max 255 characters'),
      body('action')
        .isIn(Object.values(ExceptionPublisherAction))
        .withMessage('Action must be one of: RESOLVE, ESCALATE, IGNORE'),
      body('closureRemark')
        .isString()
        .isLength({ min: 1, max: 1000 })
        .withMessage('ClosureRemark is required and must be max 1000 characters')
    ];
  }

  /**
   * Publishes a business exception
   */
  async publishException(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { tenantId, definitionCode, exceptionInfo, message } = req.body;

      await this.cronusService.publishBusinessException(
        tenantId,
        definitionCode,
        exceptionInfo,
        message
      );

      res.status(201).json({
        success: true,
        message: 'Business exception published successfully',
        data: {
          tenantId,
          definitionCode,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to publish business exception',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Closes a business exception
   */
  async closeException(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { tenantId, definitionCode, idempotentKey, action, closureRemark } = req.body;

      await this.cronusService.closeBusinessException(
        tenantId,
        definitionCode,
        idempotentKey,
        action as ExceptionPublisherAction,
        closureRemark
      );

      res.status(200).json({
        success: true,
        message: 'Business exception closed successfully',
        data: {
          tenantId,
          definitionCode,
          idempotentKey,
          action,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to close business exception',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}
