import { Request, Response } from 'express';
import { body, validationResult } from 'express-validator';
import { CronusService } from '../services/CronusService';

/**
 * Controller for handling business observation endpoints
 */
export class ObservationController {
  constructor(private readonly cronusService: CronusService) {}

  /**
   * Validation rules for publishing business observation
   */
  static publishValidationRules() {
    return [
      body('tenantId')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9.-]+$/)
        .withMessage('TenantId must be alphanumeric with dots and hyphens, max 100 characters'),
      body('definitionCode')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('DefinitionCode must be alphanumeric with underscores and hyphens, max 100 characters'),
      body('observationInfo')
        .optional()
        .isObject()
        .withMessage('ObservationInfo must be an object'),
      body('remarks')
        .optional()
        .isString()
        .isLength({ max: 1000 })
        .withMessage('Remarks must be a string with max 1000 characters')
    ];
  }

  /**
   * Validation rules for resolving business observation
   */
  static resolveValidationRules() {
    return [
      body('tenantId')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9.-]+$/)
        .withMessage('TenantId must be alphanumeric with dots and hyphens, max 100 characters'),
      body('definitionCode')
        .isString()
        .isLength({ min: 1, max: 100 })
        .matches(/^[a-zA-Z0-9_-]+$/)
        .withMessage('DefinitionCode must be alphanumeric with underscores and hyphens, max 100 characters'),
      body('idempotentKey')
        .isString()
        .isLength({ min: 1, max: 255 })
        .withMessage('IdempotentKey is required and must be max 255 characters'),
      body('observationInfo')
        .optional()
        .isObject()
        .withMessage('ObservationInfo must be an object'),
      body('remarks')
        .optional()
        .isString()
        .isLength({ max: 1000 })
        .withMessage('Remarks must be a string with max 1000 characters')
    ];
  }

  /**
   * Publishes a business observation
   */
  async publishObservation(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { tenantId, definitionCode, observationInfo, remarks } = req.body;

      await this.cronusService.publishBusinessObservation(
        tenantId,
        definitionCode,
        observationInfo || {},
        remarks || ''
      );

      res.status(201).json({
        success: true,
        message: 'Business observation published successfully',
        data: {
          tenantId,
          definitionCode,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to publish business observation',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Resolves a business observation
   */
  async resolveObservation(req: Request, res: Response): Promise<void> {
    try {
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array()
        });
        return;
      }

      const { tenantId, definitionCode, idempotentKey, observationInfo, remarks } = req.body;

      await this.cronusService.resolveBusinessObservation(
        tenantId,
        definitionCode,
        idempotentKey,
        observationInfo || {},
        remarks || ''
      );

      res.status(200).json({
        success: true,
        message: 'Business observation resolved successfully',
        data: {
          tenantId,
          definitionCode,
          idempotentKey,
          timestamp: new Date().toISOString()
        }
      });
    } catch (error) {
      res.status(500).json({
        success: false,
        message: 'Failed to resolve business observation',
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }
}
