import 'reflect-metadata';
import { createApp, setupGracefulShutdown } from './app';
import { loadEnvironmentConfig } from './config/environment';
import { WinstonLogger } from '@zeta/cronus-client';

/**
 * Main entry point for the Cronus Service
 */
async function main(): Promise<void> {
  const logger = new WinstonLogger();
  
  try {
    // Load configuration
    const envConfig = loadEnvironmentConfig();
    
    logger.info('Starting Cronus Service', {
      appName: envConfig.appName,
      clusterName: envConfig.clusterName,
      nodeEnv: envConfig.nodeEnv,
      port: envConfig.port
    });

    // Create Express app
    const app = createApp(envConfig);

    // Start server
    const server = app.listen(envConfig.port, () => {
      logger.info('Cronus Service started successfully', {
        port: envConfig.port,
        environment: envConfig.nodeEnv,
        pid: process.pid
      });
    });

    // Setup graceful shutdown
    setupGracefulShutdown(server, logger);

  } catch (error) {
    logger.error('Failed to start Cronus Service', {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined
    });
    process.exit(1);
  }
}

// Start the application
if (require.main === module) {
  main().catch((error) => {
    console.error('Fatal error during startup:', error);
    process.exit(1);
  });
}
