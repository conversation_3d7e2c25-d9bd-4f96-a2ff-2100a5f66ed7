{"name": "cronus-service", "version": "1.0.0", "description": "Production-grade Node.js service using Cronus Client library", "main": "dist/index.js", "scripts": {"build": "tsc", "build:watch": "tsc --watch", "start": "node dist/index.js", "start:dev": "ts-node src/index.ts", "start:watch": "nodemon --exec ts-node src/index.ts", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "docker:build": "docker build -t cronus-service .", "docker:run": "docker run -p 3000:3000 cronus-service"}, "keywords": ["cronus", "microservice", "business-exception", "nodejs"], "author": "Zeta Enterprise", "license": "MIT", "dependencies": {"@zeta/cronus-client": "file:../cronus-client", "express": "^4.18.2", "helmet": "^7.0.0", "cors": "^2.8.5", "compression": "^1.7.4", "dotenv": "^16.3.1", "winston": "^3.10.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "uuid": "^9.0.0", "reflect-metadata": "^0.1.13"}, "devDependencies": {"@types/express": "^4.17.17", "@types/cors": "^2.8.13", "@types/compression": "^1.7.2", "@types/jest": "^29.5.5", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "jest": "^29.6.2", "nodemon": "^3.0.1", "rimraf": "^5.0.1", "supertest": "^6.3.3", "ts-jest": "^29.1.1", "ts-node": "^10.9.1", "typescript": "^5.1.6"}, "engines": {"node": ">=18.0.0"}}