# Multi-stage build for production optimization
FROM node:18-alpine AS builder

# Set working directory
WORKDIR /app

# Copy package files
COPY package*.json ./
COPY packages/cronus-client/package*.json ./packages/cronus-client/
COPY packages/cronus-service/package*.json ./packages/cronus-service/

# Install dependencies
RUN npm ci --only=production

# Copy source code
COPY packages/cronus-client/ ./packages/cronus-client/
COPY packages/cronus-service/ ./packages/cronus-service/
COPY tsconfig.json ./

# Build the application
RUN npm run build

# Production stage
FROM node:18-alpine AS production

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S cronus -u 1001

# Set working directory
WORKDIR /app

# Copy package files
COPY --from=builder /app/package*.json ./
COPY --from=builder /app/packages/cronus-client/package*.json ./packages/cronus-client/
COPY --from=builder /app/packages/cronus-service/package*.json ./packages/cronus-service/

# Install only production dependencies
RUN npm ci --only=production && npm cache clean --force

# Copy built application
COPY --from=builder /app/packages/cronus-client/dist/ ./packages/cronus-client/dist/
COPY --from=builder /app/packages/cronus-service/dist/ ./packages/cronus-service/dist/

# Change ownership to non-root user
RUN chown -R cronus:nodejs /app
USER cronus

# Expose port
EXPOSE 3000

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:3000/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

# Start the application
CMD ["node", "packages/cronus-service/dist/index.js"]
