# Server Configuration
PORT=3000
NODE_ENV=development
LOG_LEVEL=info

# Application Configuration
APP_NAME=cronus-service
CLUSTER_NAME=rhea

# SSO Configuration
SSO_BASE_URL=https://sso-pp.zetaapps.in
CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID=your-app-auth-profile-id
CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY=your-app-private-key
CRUX_LLT_GOD_OAUTH_CLIENT_ID=your-client-id
CRUX_LLT_GOD_OAUTH_CLIENT_SECRET=your-client-secret
CRUX_LLT_GOD_OAUTH_DOMAIN_ID=0-admin.India
CRUX_LLT_GOD_OAUTH_SCOPE=admin
CRUX_LLT_TOKEN_BUFFER_TIME=2700000

# Delta Configuration
DELTA_V2_URL=https://elenchos.internal.mum1-pp.zetaapps.in/delta/api/v2/
DELTA_V2_ENCRYPTION_BASE64_SECRET=your-delta-encryption-secret

# Schaas Configuration
SCHAAS_BASE_URL=https://appinfra.internal.mum1-pp.zetaapps.in/schaas/api/v1/

# Proteus Configuration
PROTEUS_ENDPOINT=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
CERTSTORE_PROTEUS_ENDPOINT=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in
SESSIONS_PROTEUS_ENDPOINT=https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# CORS Configuration
CORS_ORIGIN=*

# Atropos Configuration (Optional)
ATROPOS_BASE_URL=https://atropos.internal.zeta.in
ATROPOS_AUTH_TOKEN=your-atropos-auth-token
