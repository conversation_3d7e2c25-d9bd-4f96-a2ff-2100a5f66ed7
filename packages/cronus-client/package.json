{"name": "@zeta/cronus-client", "version": "1.0.12", "description": "JavaScript/TypeScript client library for Cronus business exception handling", "main": "dist/index.js", "types": "dist/index.d.ts", "files": ["dist/**/*", "README.md"], "scripts": {"build": "tsc", "build:watch": "tsc --watch", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "lint": "eslint src/**/*.ts", "lint:fix": "eslint src/**/*.ts --fix", "clean": "<PERSON><PERSON><PERSON> dist", "prepublishOnly": "npm run clean && npm run build"}, "keywords": ["cronus", "business-exception", "error-handling", "observability", "zeta"], "author": "Zeta Enterprise", "license": "MIT", "dependencies": {"axios": "^1.5.0", "uuid": "^9.0.0", "winston": "^3.10.0"}, "devDependencies": {"@types/jest": "^29.5.5", "@types/node": "^20.5.0", "@types/uuid": "^9.0.2", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.45.0", "jest": "^29.6.2", "rimraf": "^5.0.1", "ts-jest": "^29.1.1", "typescript": "^5.1.6"}, "peerDependencies": {"reflect-metadata": "^0.1.13"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "https://github.com/Zeta-Enterprise/cronus-client-js.git"}}