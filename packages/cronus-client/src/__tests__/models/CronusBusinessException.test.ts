import { CronusBusinessException } from '../../models/CronusBusinessException';

describe('CronusBusinessException', () => {
  const testData = {
    tenantId: 'test-tenant',
    definitionCode: 'TEST_EXCEPTION',
    idempotentKey: 'test-key-123',
    exceptionInfo: { field1: 'value1', field2: 42 },
    message: 'Test exception message'
  };

  describe('constructor', () => {
    it('should create exception with all parameters', () => {
      const exception = new CronusBusinessException(
        testData.tenantId,
        testData.definitionCode,
        testData.idempotentKey,
        testData.exceptionInfo,
        testData.message
      );

      expect(exception.getTenantId()).toBe(testData.tenantId);
      expect(exception.getBusinessExceptionDefinitionCode()).toBe(testData.definitionCode);
      expect(exception.getIdempotentKey()).toBe(testData.idempotentKey);
      expect(exception.getExceptionInfo()).toEqual(testData.exceptionInfo);
      expect(exception.message).toBe(testData.message);
      expect(exception.name).toBe('CronusBusinessException');
    });

    it('should create exception with default message', () => {
      const exception = new CronusBusinessException(
        testData.tenantId,
        testData.definitionCode,
        testData.idempotentKey
      );

      expect(exception.message).toBe(`Business Exception: ${testData.definitionCode}`);
    });

    it('should create exception with empty exception info', () => {
      const exception = new CronusBusinessException(
        testData.tenantId,
        testData.definitionCode,
        testData.idempotentKey
      );

      expect(exception.getExceptionInfo()).toEqual({});
    });
  });

  describe('factory method', () => {
    it('should create exception using static create method', () => {
      const exception = CronusBusinessException.create(
        testData.tenantId,
        testData.definitionCode,
        testData.idempotentKey,
        testData.exceptionInfo,
        testData.message
      );

      expect(exception).toBeInstanceOf(CronusBusinessException);
      expect(exception.getTenantId()).toBe(testData.tenantId);
    });
  });

  describe('builder pattern', () => {
    it('should create exception using builder', () => {
      const exception = CronusBusinessException.builder()
        .setTenantId(testData.tenantId)
        .setBusinessExceptionDefinitionCode(testData.definitionCode)
        .setIdempotentKey(testData.idempotentKey)
        .setExceptionInfo(testData.exceptionInfo)
        .setMessage(testData.message)
        .build();

      expect(exception.getTenantId()).toBe(testData.tenantId);
      expect(exception.getBusinessExceptionDefinitionCode()).toBe(testData.definitionCode);
      expect(exception.getIdempotentKey()).toBe(testData.idempotentKey);
      expect(exception.getExceptionInfo()).toEqual(testData.exceptionInfo);
      expect(exception.message).toBe(testData.message);
    });

    it('should add individual exception info using builder', () => {
      const exception = CronusBusinessException.builder()
        .setTenantId(testData.tenantId)
        .setBusinessExceptionDefinitionCode(testData.definitionCode)
        .setIdempotentKey(testData.idempotentKey)
        .addExceptionInfo('key1', 'value1')
        .addExceptionInfo('key2', 'value2')
        .build();

      expect(exception.getExceptionInfo()).toEqual({
        key1: 'value1',
        key2: 'value2'
      });
    });

    it('should throw error when required fields are missing', () => {
      expect(() => {
        CronusBusinessException.builder().build();
      }).toThrow('TenantId is required');

      expect(() => {
        CronusBusinessException.builder()
          .setTenantId(testData.tenantId)
          .build();
      }).toThrow('BusinessExceptionDefinitionCode is required');

      expect(() => {
        CronusBusinessException.builder()
          .setTenantId(testData.tenantId)
          .setBusinessExceptionDefinitionCode(testData.definitionCode)
          .build();
      }).toThrow('IdempotentKey is required');
    });
  });

  describe('toJSON', () => {
    it('should serialize to JSON correctly', () => {
      const exception = new CronusBusinessException(
        testData.tenantId,
        testData.definitionCode,
        testData.idempotentKey,
        testData.exceptionInfo,
        testData.message
      );

      const json = exception.toJSON();

      expect(json).toEqual({
        name: 'CronusBusinessException',
        message: testData.message,
        tenantId: testData.tenantId,
        businessExceptionDefinitionCode: testData.definitionCode,
        idempotentKey: testData.idempotentKey,
        exceptionInfo: testData.exceptionInfo,
        stack: expect.any(String)
      });
    });
  });

  describe('getExceptionInfo immutability', () => {
    it('should return a copy of exception info to prevent mutation', () => {
      const originalInfo = { field: 'original' };
      const exception = new CronusBusinessException(
        testData.tenantId,
        testData.definitionCode,
        testData.idempotentKey,
        originalInfo
      );

      const retrievedInfo = exception.getExceptionInfo();
      retrievedInfo.field = 'modified';

      expect(exception.getExceptionInfo().field).toBe('original');
    });
  });
});
