import axios from 'axios';
import { AtroposPublisherClient, PublishingException } from '../../core/AtroposPublisherClient';
import { CronusConfig } from '../../models/interfaces';
import { PubSubEvent, OperationType, TopicScope } from '../../models/events';
import { NoOpLogger } from '../../utils/Logger';

// Mock axios
jest.mock('axios');
const mockedAxios = axios as jest.Mocked<typeof axios>;

describe('AtroposPublisherClient', () => {
  let client: AtroposPublisherClient;
  let mockConfig: CronusConfig;
  let logger: NoOpLogger;

  beforeEach(() => {
    logger = new NoOpLogger();
    mockConfig = {
      appName: 'test-app',
      clusterName: 'test-cluster',
      sso: {
        baseUrl: 'https://sso.test.com',
        oauth: {
          appAuthProfileId: 'test-profile',
          appPrivateKey: 'test-key',
          clientId: 'test-client',
          clientSecret: 'test-secret',
          domainId: 'test-domain',
          scope: 'test-scope'
        },
        tokenBufferTime: 3600000
      },
      delta: {
        v2Url: 'https://delta.test.com',
        encryptionBase64Secret: 'test-secret'
      },
      schaas: {
        baseUrl: 'https://schaas.test.com'
      },
      proteus: {
        endpoint: 'https://proteus.test.com',
        certstoreEndpoint: 'https://certstore.test.com',
        sessionsEndpoint: 'https://sessions.test.com'
      }
    };

    // Mock axios.create
    const mockAxiosInstance = {
      post: jest.fn(),
      interceptors: {
        request: { use: jest.fn() },
        response: { use: jest.fn() }
      }
    };
    mockedAxios.create.mockReturnValue(mockAxiosInstance as any);

    client = new AtroposPublisherClient(mockConfig, logger);
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should create client with correct configuration', () => {
      expect(mockedAxios.create).toHaveBeenCalledWith({
        timeout: 30000,
        headers: {
          'Content-Type': 'application/json',
          'User-Agent': `cronus-client-js/${mockConfig.appName}`,
          'X-JID': expect.stringMatching(/^\d+@test-app\.services\.olympus$/)
        }
      });
    });

    it('should setup request and response interceptors', () => {
      const mockAxiosInstance = mockedAxios.create.mock.results[0].value;
      expect(mockAxiosInstance.interceptors.request.use).toHaveBeenCalled();
      expect(mockAxiosInstance.interceptors.response.use).toHaveBeenCalled();
    });
  });

  describe('publish', () => {
    let mockEvent: PubSubEvent;
    let mockAxiosInstance: any;

    beforeEach(() => {
      mockEvent = {
        data: { test: 'data' },
        tenant: 'test-tenant',
        tags: [{ key: 'test', value: 'tag' }],
        objectType: 'TestObject',
        operationType: OperationType.CREATED,
        objectId: 'test-object-id',
        stateMachineState: 'NONE',
        topicScope: TopicScope.TENANT,
        sourceAttributes: [{ name: 'test', value: 'attribute' }]
      };

      mockAxiosInstance = mockedAxios.create.mock.results[0].value;
      mockAxiosInstance.post.mockResolvedValue({ status: 200 });
    });

    it('should publish event successfully', async () => {
      await client.publish(mockEvent);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        expect.stringContaining('_tenant_test-tenant_TestObject'),
        expect.objectContaining({
          id: expect.any(String),
          timestamp: expect.any(String),
          source: expect.stringMatching(/^\d+@test-app\.services\.olympus$/),
          type: 'TestObject.CREATED',
          data: mockEvent.data,
          tenant: mockEvent.tenant,
          tags: mockEvent.tags,
          objectType: mockEvent.objectType,
          operationType: mockEvent.operationType,
          objectId: mockEvent.objectId,
          stateMachineState: mockEvent.stateMachineState,
          topicScope: mockEvent.topicScope,
          sourceAttributes: mockEvent.sourceAttributes
        })
      );
    });

    it('should throw PublishingException on HTTP error', async () => {
      const error = new Error('Network error');
      mockAxiosInstance.post.mockRejectedValue(error);

      await expect(client.publish(mockEvent)).rejects.toThrow(PublishingException);
      await expect(client.publish(mockEvent)).rejects.toThrow('Failed to publish event: Network error');
    });

    it('should construct correct topic name', async () => {
      await client.publish(mockEvent);

      expect(mockAxiosInstance.post).toHaveBeenCalledWith(
        expect.stringContaining('_tenant_test-tenant_TestObject'),
        expect.any(Object)
      );
    });
  });

  describe('static helper methods', () => {
    it('should create tag correctly', () => {
      const tag = AtroposPublisherClient.createTag('testKey', 'testValue');
      expect(tag).toEqual({ key: 'testKey', value: 'testValue' });
    });

    it('should create name-value pair correctly', () => {
      const pair = AtroposPublisherClient.createNameValuePair('testName', 'testValue');
      expect(pair).toEqual({ name: 'testName', value: 'testValue' });
    });
  });

  describe('error handling', () => {
    it('should handle non-Error objects in catch blocks', async () => {
      const mockAxiosInstance = mockedAxios.create.mock.results[0].value;
      mockAxiosInstance.post.mockRejectedValue('String error');

      const mockEvent: PubSubEvent = {
        data: {},
        tenant: 'test-tenant',
        tags: [],
        objectType: 'TestObject',
        operationType: OperationType.CREATED,
        objectId: 'test-id',
        stateMachineState: 'NONE',
        topicScope: TopicScope.TENANT,
        sourceAttributes: []
      };

      await expect(client.publish(mockEvent)).rejects.toThrow(PublishingException);
    });
  });
});
