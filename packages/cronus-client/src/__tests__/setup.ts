import 'reflect-metadata';

// Global test setup
beforeEach(() => {
  // Clear all environment variables that might affect tests
  delete process.env.LOG_LEVEL;
  delete process.env.ATROPOS_BASE_URL;
  delete process.env.ATROPOS_AUTH_TOKEN;
});

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};
