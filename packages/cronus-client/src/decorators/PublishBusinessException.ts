import 'reflect-metadata';
import { BusinessException } from '../models/interfaces';
import { Publisher } from '../models/interfaces';
import { Logger } from '../utils/Logger';

/**
 * Metadata key for storing business exception handler
 */
const BUSINESS_EXCEPTION_HANDLER_KEY = Symbol('businessExceptionHandler');

/**
 * Metadata key for storing logger
 */
const LOGGER_KEY = Symbol('logger');

/**
 * Interface for classes that can have business exception publishing
 */
export interface BusinessExceptionPublishable {
  [BUSINESS_EXCEPTION_HANDLER_KEY]?: Publisher;
  [LOGGER_KEY]?: Logger;
}

/**
 * Decorator for automatic business exception publishing
 * Similar to @PublishBusinessException in Java
 */
export function PublishBusinessException(target: any, propertyName: string, descriptor: PropertyDescriptor): PropertyDescriptor {
  const method = descriptor.value;

  descriptor.value = async function (this: BusinessExceptionPublishable, ...args: any[]) {
    const handler = this[BUSINESS_EXCEPTION_HANDLER_KEY];
    const logger = this[LOGGER_KEY];

    if (!handler) {
      logger?.warn('No business exception handler configured for automatic publishing', {
        className: this.constructor.name,
        methodName: propertyName
      });
      return method.apply(this, args);
    }

    try {
      const result = await method.apply(this, args);
      return result;
    } catch (error) {
      // Check if the error is a BusinessException
      if (isBusinessException(error)) {
        try {
          logger?.info('Publishing business exception automatically', {
            className: this.constructor.name,
            methodName: propertyName,
            tenantId: error.getTenantId(),
            definitionCode: error.getBusinessExceptionDefinitionCode(),
            idempotentKey: error.getIdempotentKey()
          });

          await handler.publish(error);

          logger?.info('Successfully published business exception', {
            className: this.constructor.name,
            methodName: propertyName,
            tenantId: error.getTenantId(),
            definitionCode: error.getBusinessExceptionDefinitionCode(),
            idempotentKey: error.getIdempotentKey()
          });
        } catch (publishError) {
          logger?.error('Failed to publish business exception', {
            className: this.constructor.name,
            methodName: propertyName,
            tenantId: error.getTenantId(),
            definitionCode: error.getBusinessExceptionDefinitionCode(),
            idempotentKey: error.getIdempotentKey(),
            publishError: publishError instanceof Error ? publishError.message : String(publishError)
          });
        }
      }

      // Re-throw the original error
      throw error;
    }
  };

  return descriptor;
}

/**
 * Class decorator to inject business exception handler and logger
 */
export function BusinessExceptionEnabled(handler: Publisher, logger?: Logger) {
  return function <T extends { new (...args: any[]): {} }>(constructor: T) {
    return class extends constructor implements BusinessExceptionPublishable {
      [BUSINESS_EXCEPTION_HANDLER_KEY] = handler;
      [LOGGER_KEY] = logger;
    };
  };
}

/**
 * Method decorator for handling async methods that return promises
 */
export function PublishBusinessExceptionAsync(target: any, propertyName: string, descriptor: PropertyDescriptor): PropertyDescriptor {
  const method = descriptor.value;

  descriptor.value = function (this: BusinessExceptionPublishable, ...args: any[]) {
    const handler = this[BUSINESS_EXCEPTION_HANDLER_KEY];
    const logger = this[LOGGER_KEY];

    if (!handler) {
      logger?.warn('No business exception handler configured for automatic publishing', {
        className: this.constructor.name,
        methodName: propertyName
      });
      return method.apply(this, args);
    }

    const result = method.apply(this, args);

    // Handle Promise-based methods
    if (result && typeof result.catch === 'function') {
      return result.catch(async (error: any) => {
        if (isBusinessException(error)) {
          try {
            logger?.info('Publishing business exception automatically (async)', {
              className: this.constructor.name,
              methodName: propertyName,
              tenantId: error.getTenantId(),
              definitionCode: error.getBusinessExceptionDefinitionCode(),
              idempotentKey: error.getIdempotentKey()
            });

            await handler.publish(error);

            logger?.info('Successfully published business exception (async)', {
              className: this.constructor.name,
              methodName: propertyName,
              tenantId: error.getTenantId(),
              definitionCode: error.getBusinessExceptionDefinitionCode(),
              idempotentKey: error.getIdempotentKey()
            });
          } catch (publishError) {
            logger?.error('Failed to publish business exception (async)', {
              className: this.constructor.name,
              methodName: propertyName,
              tenantId: error.getTenantId(),
              definitionCode: error.getBusinessExceptionDefinitionCode(),
              idempotentKey: error.getIdempotentKey(),
              publishError: publishError instanceof Error ? publishError.message : String(publishError)
            });
          }
        }

        // Re-throw the original error
        throw error;
      });
    }

    return result;
  };

  return descriptor;
}

/**
 * Type guard to check if an error is a BusinessException
 */
function isBusinessException(error: any): error is BusinessException {
  return error &&
    typeof error.getTenantId === 'function' &&
    typeof error.getBusinessExceptionDefinitionCode === 'function' &&
    typeof error.getIdempotentKey === 'function' &&
    typeof error.getExceptionInfo === 'function';
}

/**
 * Utility function to set up business exception handling for a class instance
 */
export function setupBusinessExceptionHandling(
  instance: any,
  handler: Publisher,
  logger?: Logger
): void {
  (instance as BusinessExceptionPublishable)[BUSINESS_EXCEPTION_HANDLER_KEY] = handler;
  if (logger) {
    (instance as BusinessExceptionPublishable)[LOGGER_KEY] = logger;
  }
}
