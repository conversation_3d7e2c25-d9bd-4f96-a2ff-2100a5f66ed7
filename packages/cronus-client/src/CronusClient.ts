import { CronusConfig, Publisher, BusinessObservationHandler } from './models/interfaces';
import { AtroposPublisherClient } from './core/AtroposPublisherClient';
import { AtroposBusinessExceptionHandler } from './core/AtroposBusinessExceptionHandler';
import { AtroposBusinessObservationHandler } from './core/AtroposBusinessObservationHandler';
import { BusinessExceptionValidator } from './core/BusinessExceptionValidator';
import { <PERSON><PERSON>, WinstonLogger } from './utils/Logger';

/**
 * Main client class for Cronus business exception handling
 */
export class CronusClient {
  private readonly config: CronusConfig;
  private readonly logger: Logger;
  private readonly atroposPublisherClient: AtroposPublisherClient;
  private readonly businessExceptionHandler: Publisher;
  private readonly businessObservationHandler: BusinessObservationHandler;
  private readonly businessExceptionValidator: BusinessExceptionValidator;

  constructor(config: CronusConfig, logger?: Logger) {
    this.config = config;
    this.logger = logger || new WinstonLogger();
    
    // Initialize core components
    this.businessExceptionValidator = new BusinessExceptionValidator();
    this.atroposPublisherClient = new AtroposPublisherClient(config, this.logger);
    
    this.businessExceptionHandler = new AtroposBusinessExceptionHandler(
      this.atroposPublisherClient,
      this.businessExceptionValidator,
      this.logger,
      config.appName
    );
    
    this.businessObservationHandler = new AtroposBusinessObservationHandler(
      this.atroposPublisherClient,
      this.logger,
      config.appName
    );

    this.logger.info('Cronus Client initialized', {
      appName: config.appName,
      clusterName: config.clusterName
    });
  }

  /**
   * Gets the business exception handler
   */
  getBusinessExceptionHandler(): Publisher {
    return this.businessExceptionHandler;
  }

  /**
   * Gets the business observation handler
   */
  getBusinessObservationHandler(): BusinessObservationHandler {
    return this.businessObservationHandler;
  }

  /**
   * Gets the business exception validator
   */
  getBusinessExceptionValidator(): BusinessExceptionValidator {
    return this.businessExceptionValidator;
  }

  /**
   * Gets the logger instance
   */
  getLogger(): Logger {
    return this.logger;
  }

  /**
   * Gets the configuration
   */
  getConfig(): CronusConfig {
    return { ...this.config };
  }

  /**
   * Creates a new CronusClient instance with the provided configuration
   */
  static create(config: CronusConfig, logger?: Logger): CronusClient {
    return new CronusClient(config, logger);
  }
}
