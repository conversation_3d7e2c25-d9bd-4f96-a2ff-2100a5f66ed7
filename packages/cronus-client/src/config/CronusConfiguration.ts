import { CronusConfig } from '../models/interfaces';

/**
 * Configuration builder for Cronus Client
 */
export class CronusConfigurationBuilder {
  private config: Partial<CronusConfig> = {};

  setAppName(appName: string): this {
    this.config.appName = appName;
    return this;
  }

  setClusterName(clusterName: string): this {
    this.config.clusterName = clusterName;
    return this;
  }

  setSsoConfig(ssoConfig: CronusConfig['sso']): this {
    this.config.sso = ssoConfig;
    return this;
  }

  setDeltaConfig(deltaConfig: CronusConfig['delta']): this {
    this.config.delta = deltaConfig;
    return this;
  }

  setSchaasConfig(schaasConfig: CronusConfig['schaas']): this {
    this.config.schaas = schaasConfig;
    return this;
  }

  setProteusConfig(proteusConfig: CronusConfig['proteus']): this {
    this.config.proteus = proteusConfig;
    return this;
  }

  build(): CronusConfig {
    if (!this.config.appName) {
      throw new Error('App name is required');
    }
    if (!this.config.clusterName) {
      throw new Error('Cluster name is required');
    }
    if (!this.config.sso) {
      throw new Error('SSO configuration is required');
    }
    if (!this.config.delta) {
      throw new Error('Delta configuration is required');
    }
    if (!this.config.schaas) {
      throw new Error('Schaas configuration is required');
    }
    if (!this.config.proteus) {
      throw new Error('Proteus configuration is required');
    }

    return this.config as CronusConfig;
  }
}

/**
 * Configuration loader that supports environment variables
 */
export class CronusConfigurationLoader {
  /**
   * Loads configuration from environment variables
   */
  static fromEnvironment(): CronusConfig {
    const builder = new CronusConfigurationBuilder();

    return builder
      .setAppName(this.getRequiredEnv('APP_NAME'))
      .setClusterName(this.getRequiredEnv('CLUSTER_NAME'))
      .setSsoConfig({
        baseUrl: this.getRequiredEnv('SSO_BASE_URL'),
        oauth: {
          appAuthProfileId: this.getRequiredEnv('CRUX_LLT_GOD_OAUTH_APP_AUTH_PROFILE_ID'),
          appPrivateKey: this.getRequiredEnv('CRUX_LLT_GOD_OAUTH_APP_PRIVATE_KEY'),
          clientId: this.getRequiredEnv('CRUX_LLT_GOD_OAUTH_CLIENT_ID'),
          clientSecret: this.getRequiredEnv('CRUX_LLT_GOD_OAUTH_CLIENT_SECRET'),
          domainId: this.getEnv('CRUX_LLT_GOD_OAUTH_DOMAIN_ID', '0-admin.India'),
          scope: this.getEnv('CRUX_LLT_GOD_OAUTH_SCOPE', 'admin')
        },
        tokenBufferTime: parseInt(this.getEnv('CRUX_LLT_TOKEN_BUFFER_TIME', '2700000'))
      })
      .setDeltaConfig({
        v2Url: this.getRequiredEnv('DELTA_V2_URL'),
        encryptionBase64Secret: this.getRequiredEnv('DELTA_V2_ENCRYPTION_BASE64_SECRET')
      })
      .setSchaasConfig({
        baseUrl: this.getRequiredEnv('SCHAAS_BASE_URL')
      })
      .setProteusConfig({
        endpoint: this.getRequiredEnv('PROTEUS_ENDPOINT'),
        certstoreEndpoint: this.getRequiredEnv('CERTSTORE_PROTEUS_ENDPOINT'),
        sessionsEndpoint: this.getRequiredEnv('SESSIONS_PROTEUS_ENDPOINT')
      })
      .build();
  }

  /**
   * Loads configuration from a configuration object
   */
  static fromObject(configObject: Partial<CronusConfig>): CronusConfig {
    const builder = new CronusConfigurationBuilder();

    if (configObject.appName) builder.setAppName(configObject.appName);
    if (configObject.clusterName) builder.setClusterName(configObject.clusterName);
    if (configObject.sso) builder.setSsoConfig(configObject.sso);
    if (configObject.delta) builder.setDeltaConfig(configObject.delta);
    if (configObject.schaas) builder.setSchaasConfig(configObject.schaas);
    if (configObject.proteus) builder.setProteusConfig(configObject.proteus);

    return builder.build();
  }

  /**
   * Loads configuration from a JSON file
   */
  static fromFile(filePath: string): CronusConfig {
    try {
      const fs = require('fs');
      const configData = JSON.parse(fs.readFileSync(filePath, 'utf8'));
      return this.fromObject(configData);
    } catch (error) {
      throw new Error(`Failed to load configuration from file ${filePath}: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  private static getRequiredEnv(key: string): string {
    const value = process.env[key];
    if (!value) {
      throw new Error(`Required environment variable ${key} is not set`);
    }
    return value;
  }

  private static getEnv(key: string, defaultValue: string): string {
    return process.env[key] || defaultValue;
  }
}

/**
 * Default configuration for development/testing
 */
export const DEFAULT_CONFIG: Partial<CronusConfig> = {
  appName: 'cronus-client-test',
  clusterName: 'rhea',
  sso: {
    baseUrl: 'https://sso-pp.zetaapps.in',
    oauth: {
      appAuthProfileId: '',
      appPrivateKey: '',
      clientId: '',
      clientSecret: '',
      domainId: '0-admin.India',
      scope: 'admin'
    },
    tokenBufferTime: 2700000
  },
  delta: {
    v2Url: 'https://elenchos.internal.mum1-pp.zetaapps.in/delta/api/v2/',
    encryptionBase64Secret: ''
  },
  schaas: {
    baseUrl: 'https://appinfra.internal.mum1-pp.zetaapps.in/schaas/api/v1/'
  },
  proteus: {
    endpoint: 'https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in',
    certstoreEndpoint: 'https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in',
    sessionsEndpoint: 'https://proteus-cipher.mum1-pp.zeta.in/proteus/zeta.in'
  }
};
