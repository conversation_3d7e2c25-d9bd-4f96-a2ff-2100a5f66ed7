import { BusinessException, BusinessObservation, CloseException } from './interfaces';

/**
 * Event data for business exception instances
 */
export interface BusinessExceptionInstanceEvent {
  tenantId: string;
  businessExceptionDefinitionCode: string;
  idempotentKey: string;
  exceptionInfo: Record<string, any>;
  timestamp: Date;
  sourceApplication: string;
  correlationId?: string;
}

/**
 * Event data for closing business exception instances
 */
export interface CloseExceptionInstanceEvent {
  tenantId: string;
  businessExceptionDefinitionCode: string;
  idempotentKey: string;
  action: string;
  closureRemark: string;
  timestamp: Date;
  sourceApplication: string;
  correlationId?: string;
}

/**
 * Event data for business observations
 */
export interface BusinessObservationEvent {
  tenantId: string;
  definitionCode: string;
  idempotentKey: string;
  observationInfo: Record<string, any>;
  remarks: string;
  timestamp: Date;
  sourceApplication: string;
  correlationId?: string;
}

/**
 * PubSub event structure for Atropos
 */
export interface PubSubEvent {
  data: any;
  tenant: string;
  tags: Tag[];
  objectType: string;
  operationType: OperationType;
  objectId: string;
  stateMachineState: string;
  topicScope: TopicScope;
  sourceAttributes: NameValuePair[];
}

/**
 * Tag for categorizing events
 */
export interface Tag {
  key: string;
  value: string;
}

/**
 * Name-value pair for additional attributes
 */
export interface NameValuePair {
  name: string;
  value: string;
}

/**
 * Operation types for events
 */
export enum OperationType {
  CREATED = 'CREATED',
  UPDATED = 'UPDATED',
  DELETED = 'DELETED'
}

/**
 * Topic scopes for event routing
 */
export enum TopicScope {
  TENANT = 'TENANT',
  GLOBAL = 'GLOBAL'
}

/**
 * Factory class for creating events from domain objects
 */
export class EventFactory {
  /**
   * Creates a BusinessExceptionInstanceEvent from a BusinessException
   */
  static createBusinessExceptionInstanceEvent(
    businessException: BusinessException,
    sourceApplication: string,
    correlationId?: string
  ): BusinessExceptionInstanceEvent {
    return {
      tenantId: businessException.getTenantId(),
      businessExceptionDefinitionCode: businessException.getBusinessExceptionDefinitionCode(),
      idempotentKey: businessException.getIdempotentKey(),
      exceptionInfo: businessException.getExceptionInfo(),
      timestamp: new Date(),
      sourceApplication,
      correlationId
    };
  }

  /**
   * Creates a CloseExceptionInstanceEvent from a CloseException
   */
  static createCloseExceptionInstanceEvent(
    closeException: CloseException,
    sourceApplication: string,
    correlationId?: string
  ): CloseExceptionInstanceEvent {
    return {
      tenantId: closeException.getTenantId(),
      businessExceptionDefinitionCode: closeException.getBusinessExceptionDefinitionCode(),
      idempotentKey: closeException.getIdempotentKey(),
      action: closeException.getAction(),
      closureRemark: closeException.getClosureRemark(),
      timestamp: new Date(),
      sourceApplication,
      correlationId
    };
  }

  /**
   * Creates a BusinessObservationEvent from a BusinessObservation
   */
  static createBusinessObservationEvent(
    businessObservation: BusinessObservation,
    sourceApplication: string,
    correlationId?: string
  ): BusinessObservationEvent {
    return {
      tenantId: businessObservation.getTenantId(),
      definitionCode: businessObservation.getDefinitionCode(),
      idempotentKey: businessObservation.getIdempotentKey(),
      observationInfo: businessObservation.getObservationInfo(),
      remarks: businessObservation.getRemarks(),
      timestamp: new Date(),
      sourceApplication,
      correlationId
    };
  }
}
