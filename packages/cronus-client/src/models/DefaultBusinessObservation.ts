import { BusinessObservation } from './interfaces';

/**
 * Default implementation of BusinessObservation
 */
export class DefaultBusinessObservation implements BusinessObservation {
  private readonly tenantId: string;
  private readonly definitionCode: string;
  private readonly idempotentKey: string;
  private readonly observationInfo: Record<string, any>;
  private readonly remarks: string;

  constructor(
    tenantId: string,
    definitionCode: string,
    idempotentKey: string,
    observationInfo: Record<string, any> = {},
    remarks: string = ''
  ) {
    this.tenantId = tenantId;
    this.definitionCode = definitionCode;
    this.idempotentKey = idempotentKey;
    this.observationInfo = observationInfo;
    this.remarks = remarks;
  }

  getTenantId(): string {
    return this.tenantId;
  }

  getDefinitionCode(): string {
    return this.definitionCode;
  }

  getIdempotentKey(): string {
    return this.idempotentKey;
  }

  getObservationInfo(): Record<string, any> {
    return { ...this.observationInfo };
  }

  getRemarks(): string {
    return this.remarks;
  }

  /**
   * Creates a builder for fluent construction
   */
  static builder(): DefaultBusinessObservationBuilder {
    return new DefaultBusinessObservationBuilder();
  }

  /**
   * Converts the observation to a JSON representation
   */
  toJSON(): Record<string, any> {
    return {
      tenantId: this.tenantId,
      definitionCode: this.definitionCode,
      idempotentKey: this.idempotentKey,
      observationInfo: this.observationInfo,
      remarks: this.remarks
    };
  }
}

/**
 * Builder class for DefaultBusinessObservation
 */
export class DefaultBusinessObservationBuilder {
  private tenantId?: string;
  private definitionCode?: string;
  private idempotentKey?: string;
  private observationInfo: Record<string, any> = {};
  private remarks: string = '';

  setTenantId(tenantId: string): this {
    this.tenantId = tenantId;
    return this;
  }

  setDefinitionCode(code: string): this {
    this.definitionCode = code;
    return this;
  }

  setIdempotentKey(key: string): this {
    this.idempotentKey = key;
    return this;
  }

  setObservationInfo(info: Record<string, any>): this {
    this.observationInfo = { ...info };
    return this;
  }

  addObservationInfo(key: string, value: any): this {
    this.observationInfo[key] = value;
    return this;
  }

  setRemarks(remarks: string): this {
    this.remarks = remarks;
    return this;
  }

  build(): DefaultBusinessObservation {
    if (!this.tenantId) {
      throw new Error('TenantId is required');
    }
    if (!this.definitionCode) {
      throw new Error('DefinitionCode is required');
    }
    if (!this.idempotentKey) {
      throw new Error('IdempotentKey is required');
    }

    return new DefaultBusinessObservation(
      this.tenantId,
      this.definitionCode,
      this.idempotentKey,
      this.observationInfo,
      this.remarks
    );
  }
}
