/**
 * Core interfaces for Cronus Client business exception handling
 */

/**
 * Represents a business exception that can be published to the system
 */
export interface BusinessException {
  /**
   * Unique identifier for the tenant
   */
  getTenantId(): string;

  /**
   * Code identifying the type of business exception
   */
  getBusinessExceptionDefinitionCode(): string;

  /**
   * Unique key to ensure idempotent operations
   */
  getIdempotentKey(): string;

  /**
   * Additional information about the exception
   */
  getExceptionInfo(): Record<string, any>;
}

/**
 * Actions that can be performed when closing an exception
 */
export enum ExceptionPublisherAction {
  RESOLVE = 'RESOLVE',
  ESCALATE = 'ESCALATE',
  IGNORE = 'IGNORE'
}

/**
 * Represents a request to close/resolve a business exception
 */
export interface CloseException {
  /**
   * Unique identifier for the tenant
   */
  getTenantId(): string;

  /**
   * Code identifying the type of business exception to close
   */
  getBusinessExceptionDefinitionCode(): string;

  /**
   * Unique key to identify the exception instance
   */
  getIdempotentKey(): string;

  /**
   * Action to perform when closing the exception
   */
  getAction(): ExceptionPublisherAction;

  /**
   * Remarks explaining the closure
   */
  getClosureRemark(): string;
}

/**
 * Represents a business observation for tracking business events
 */
export interface BusinessObservation {
  /**
   * Unique identifier for the tenant
   */
  getTenantId(): string;

  /**
   * Code identifying the type of observation
   */
  getDefinitionCode(): string;

  /**
   * Unique key to ensure idempotent operations
   */
  getIdempotentKey(): string;

  /**
   * Additional information about the observation
   */
  getObservationInfo(): Record<string, any>;

  /**
   * Optional remarks about the observation
   */
  getRemarks(): string;
}

/**
 * Publisher interface for handling business exceptions
 */
export interface Publisher {
  /**
   * Publishes a business exception
   */
  publish(businessException: BusinessException): Promise<void>;

  /**
   * Closes/resolves a business exception
   */
  close(closeException: CloseException): Promise<void>;
}

/**
 * Handler interface for business observations
 */
export interface BusinessObservationHandler {
  /**
   * Publishes a business observation
   */
  publish(businessObservation: BusinessObservation): Promise<void>;

  /**
   * Resolves a business observation
   */
  resolve(businessObservation: BusinessObservation): Promise<void>;
}

/**
 * Configuration interface for Cronus Client
 */
export interface CronusConfig {
  /**
   * Application name
   */
  appName: string;

  /**
   * Cluster name
   */
  clusterName: string;

  /**
   * SSO configuration
   */
  sso: {
    baseUrl: string;
    oauth: {
      appAuthProfileId: string;
      appPrivateKey: string;
      clientId: string;
      clientSecret: string;
      domainId: string;
      scope: string;
    };
    tokenBufferTime: number;
  };

  /**
   * Delta service configuration
   */
  delta: {
    v2Url: string;
    encryptionBase64Secret: string;
  };

  /**
   * Schaas service configuration
   */
  schaas: {
    baseUrl: string;
  };

  /**
   * Proteus endpoints
   */
  proteus: {
    endpoint: string;
    certstoreEndpoint: string;
    sessionsEndpoint: string;
  };
}
