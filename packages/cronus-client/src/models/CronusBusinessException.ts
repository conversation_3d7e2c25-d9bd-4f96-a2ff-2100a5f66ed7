import { BusinessException } from './interfaces';

/**
 * Concrete implementation of BusinessException
 */
export class CronusBusinessException extends Error implements BusinessException {
  private readonly tenantId: string;
  private readonly businessExceptionDefinitionCode: string;
  private readonly idempotentKey: string;
  private readonly exceptionInfo: Record<string, any>;

  constructor(
    tenantId: string,
    businessExceptionDefinitionCode: string,
    idempotentKey: string,
    exceptionInfo: Record<string, any> = {},
    message?: string
  ) {
    super(message || `Business Exception: ${businessExceptionDefinitionCode}`);
    this.name = 'CronusBusinessException';
    this.tenantId = tenantId;
    this.businessExceptionDefinitionCode = businessExceptionDefinitionCode;
    this.idempotentKey = idempotentKey;
    this.exceptionInfo = exceptionInfo;

    // Maintains proper stack trace for where our error was thrown (only available on V8)
    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CronusBusinessException);
    }
  }

  getTenantId(): string {
    return this.tenantId;
  }

  getBusinessExceptionDefinitionCode(): string {
    return this.businessExceptionDefinitionCode;
  }

  getIdempotentKey(): string {
    return this.idempotentKey;
  }

  getExceptionInfo(): Record<string, any> {
    return { ...this.exceptionInfo };
  }

  /**
   * Factory method to create a new instance
   */
  static create(
    tenantId: string,
    businessExceptionDefinitionCode: string,
    idempotentKey: string,
    exceptionInfo: Record<string, any> = {},
    message?: string
  ): CronusBusinessException {
    return new CronusBusinessException(
      tenantId,
      businessExceptionDefinitionCode,
      idempotentKey,
      exceptionInfo,
      message
    );
  }

  /**
   * Creates a builder for fluent construction
   */
  static builder(): CronusBusinessExceptionBuilder {
    return new CronusBusinessExceptionBuilder();
  }

  /**
   * Converts the exception to a JSON representation
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      tenantId: this.tenantId,
      businessExceptionDefinitionCode: this.businessExceptionDefinitionCode,
      idempotentKey: this.idempotentKey,
      exceptionInfo: this.exceptionInfo,
      stack: this.stack
    };
  }
}

/**
 * Builder class for CronusBusinessException
 */
export class CronusBusinessExceptionBuilder {
  private tenantId?: string;
  private businessExceptionDefinitionCode?: string;
  private idempotentKey?: string;
  private exceptionInfo: Record<string, any> = {};
  private message?: string;

  setTenantId(tenantId: string): this {
    this.tenantId = tenantId;
    return this;
  }

  setBusinessExceptionDefinitionCode(code: string): this {
    this.businessExceptionDefinitionCode = code;
    return this;
  }

  setIdempotentKey(key: string): this {
    this.idempotentKey = key;
    return this;
  }

  setExceptionInfo(info: Record<string, any>): this {
    this.exceptionInfo = { ...info };
    return this;
  }

  addExceptionInfo(key: string, value: any): this {
    this.exceptionInfo[key] = value;
    return this;
  }

  setMessage(message: string): this {
    this.message = message;
    return this;
  }

  build(): CronusBusinessException {
    if (!this.tenantId) {
      throw new Error('TenantId is required');
    }
    if (!this.businessExceptionDefinitionCode) {
      throw new Error('BusinessExceptionDefinitionCode is required');
    }
    if (!this.idempotentKey) {
      throw new Error('IdempotentKey is required');
    }

    return new CronusBusinessException(
      this.tenantId,
      this.businessExceptionDefinitionCode,
      this.idempotentKey,
      this.exceptionInfo,
      this.message
    );
  }
}
