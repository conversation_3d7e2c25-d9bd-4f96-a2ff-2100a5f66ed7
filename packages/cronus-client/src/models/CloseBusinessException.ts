import { CloseException, ExceptionPublisherAction } from './interfaces';

/**
 * Concrete implementation of CloseException
 */
export class CloseBusinessException extends Error implements CloseException {
  private readonly tenantId: string;
  private readonly businessExceptionDefinitionCode: string;
  private readonly idempotentKey: string;
  private readonly action: ExceptionPublisherAction;
  private readonly closureRemark: string;

  constructor(
    tenantId: string,
    businessExceptionDefinitionCode: string,
    idempotentKey: string,
    action: ExceptionPublisherAction,
    closureRemark: string
  ) {
    super(`Closing Business Exception: ${businessExceptionDefinitionCode}`);
    this.name = 'CloseBusinessException';
    this.tenantId = tenantId;
    this.businessExceptionDefinitionCode = businessExceptionDefinitionCode;
    this.idempotentKey = idempotentKey;
    this.action = action;
    this.closureRemark = closureRemark;

    if (Error.captureStackTrace) {
      Error.captureStackTrace(this, CloseBusinessException);
    }
  }

  getTenantId(): string {
    return this.tenantId;
  }

  getBusinessExceptionDefinitionCode(): string {
    return this.businessExceptionDefinitionCode;
  }

  getIdempotentKey(): string {
    return this.idempotentKey;
  }

  getAction(): ExceptionPublisherAction {
    return this.action;
  }

  getClosureRemark(): string {
    return this.closureRemark;
  }

  /**
   * Factory method to create a new instance
   */
  static getInstance(
    tenantId: string,
    businessExceptionDefinitionCode: string,
    idempotentKey: string,
    action: ExceptionPublisherAction,
    closureRemark: string
  ): CloseBusinessException {
    return new CloseBusinessException(
      tenantId,
      businessExceptionDefinitionCode,
      idempotentKey,
      action,
      closureRemark
    );
  }

  /**
   * Creates a builder for fluent construction
   */
  static builder(): CloseBusinessExceptionBuilder {
    return new CloseBusinessExceptionBuilder();
  }

  /**
   * Converts the exception to a JSON representation
   */
  toJSON(): Record<string, any> {
    return {
      name: this.name,
      message: this.message,
      tenantId: this.tenantId,
      businessExceptionDefinitionCode: this.businessExceptionDefinitionCode,
      idempotentKey: this.idempotentKey,
      action: this.action,
      closureRemark: this.closureRemark
    };
  }
}

/**
 * Builder class for CloseBusinessException
 */
export class CloseBusinessExceptionBuilder {
  private tenantId?: string;
  private businessExceptionDefinitionCode?: string;
  private idempotentKey?: string;
  private action?: ExceptionPublisherAction;
  private closureRemark?: string;

  setTenantId(tenantId: string): this {
    this.tenantId = tenantId;
    return this;
  }

  setBusinessExceptionDefinitionCode(code: string): this {
    this.businessExceptionDefinitionCode = code;
    return this;
  }

  setIdempotentKey(key: string): this {
    this.idempotentKey = key;
    return this;
  }

  setAction(action: ExceptionPublisherAction): this {
    this.action = action;
    return this;
  }

  setClosureRemark(remark: string): this {
    this.closureRemark = remark;
    return this;
  }

  build(): CloseBusinessException {
    if (!this.tenantId) {
      throw new Error('TenantId is required');
    }
    if (!this.businessExceptionDefinitionCode) {
      throw new Error('BusinessExceptionDefinitionCode is required');
    }
    if (!this.idempotentKey) {
      throw new Error('IdempotentKey is required');
    }
    if (!this.action) {
      throw new Error('Action is required');
    }
    if (!this.closureRemark) {
      throw new Error('ClosureRemark is required');
    }

    return new CloseBusinessException(
      this.tenantId,
      this.businessExceptionDefinitionCode,
      this.idempotentKey,
      this.action,
      this.closureRemark
    );
  }
}
