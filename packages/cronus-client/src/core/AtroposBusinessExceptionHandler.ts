import { 
  BusinessExceptionInstanceEvent, 
  CloseExceptionInstanceEvent,
  PubSubEvent,
  OperationType,
  TopicScope
} from '../models/events';
import { AbstractBusinessExceptionHandler } from './AbstractBusinessExceptionHandler';
import { AtroposPublisherClient } from './AtroposPublisherClient';
import { BusinessExceptionValidator } from './BusinessExceptionValidator';
import { Logger } from '../utils/Logger';

/**
 * Concrete implementation of business exception handler using Atropos
 */
export class AtroposBusinessExceptionHandler extends AbstractBusinessExceptionHandler {
  private static readonly TOPIC_NAME_FORMAT = '_%s_%s_%s';
  private static readonly BUSINESS_EXCEPTION_OBJECT_TYPE = 'BusinessException';
  private static readonly CLOSE_EXCEPTION_OBJECT_TYPE = 'BusinessExceptionClose';

  private readonly atroposPublisherClient: AtroposPublisherClient;

  constructor(
    atroposPublisherClient: AtroposPublisherClient,
    businessExceptionValidator: BusinessExceptionValidator,
    logger: Logger,
    sourceApplication: string
  ) {
    super(businessExceptionValidator, logger, sourceApplication);
    this.atroposPublisherClient = atroposPublisherClient;
  }

  /**
   * Publishes business exception instance event to Atropos
   */
  protected async publishBusinessExceptionInstanceEvent(
    event: BusinessExceptionInstanceEvent
  ): Promise<void> {
    const pubSubEvent = this.constructPubSubEventBuilder(
      event,
      AtroposBusinessExceptionHandler.BUSINESS_EXCEPTION_OBJECT_TYPE
    );

    const topic = this.formatTopic(
      pubSubEvent.topicScope,
      pubSubEvent.tenant,
      pubSubEvent.objectType
    );

    this.logger.info('Commenced Exception Publication', {
      topic,
      tenantId: event.tenantId,
      definitionCode: event.businessExceptionDefinitionCode,
      idempotentKey: event.idempotentKey
    });

    try {
      await this.atroposPublisherClient.publish(pubSubEvent);
      
      this.logger.info('Successfully Exception Published', {
        topic,
        tenantId: event.tenantId,
        definitionCode: event.businessExceptionDefinitionCode,
        idempotentKey: event.idempotentKey
      });
    } catch (error) {
      this.logger.error('Failed to publish business exception event', {
        topic,
        tenantId: event.tenantId,
        definitionCode: event.businessExceptionDefinitionCode,
        idempotentKey: event.idempotentKey,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Publishes close exception instance event to Atropos
   */
  protected async publishCloseExceptionInstanceEvent(
    event: CloseExceptionInstanceEvent
  ): Promise<void> {
    const pubSubEvent = this.constructPubSubCloseEventBuilder(
      event,
      AtroposBusinessExceptionHandler.CLOSE_EXCEPTION_OBJECT_TYPE
    );

    const topic = this.formatTopic(
      pubSubEvent.topicScope,
      pubSubEvent.tenant,
      pubSubEvent.objectType
    );

    this.logger.info('Commenced Exception Close Publication', {
      topic,
      tenantId: event.tenantId,
      definitionCode: event.businessExceptionDefinitionCode,
      idempotentKey: event.idempotentKey,
      action: event.action
    });

    try {
      await this.atroposPublisherClient.publish(pubSubEvent);
      
      this.logger.info('Successfully Exception Close Published', {
        topic,
        tenantId: event.tenantId,
        definitionCode: event.businessExceptionDefinitionCode,
        idempotentKey: event.idempotentKey,
        action: event.action
      });
    } catch (error) {
      this.logger.error('Failed to publish close exception event', {
        topic,
        tenantId: event.tenantId,
        definitionCode: event.businessExceptionDefinitionCode,
        idempotentKey: event.idempotentKey,
        action: event.action,
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Constructs PubSub event for business exception
   */
  private constructPubSubEventBuilder(
    event: BusinessExceptionInstanceEvent,
    objectType: string
  ): PubSubEvent {
    const tag = AtroposPublisherClient.createTag(
      'businessExceptionDefinitionCode',
      event.businessExceptionDefinitionCode
    );

    const sourceAttributes = [
      AtroposPublisherClient.createNameValuePair('businessExceptionTenantID', event.tenantId),
      AtroposPublisherClient.createNameValuePair('businessExceptionDefinitionCode', event.businessExceptionDefinitionCode),
      AtroposPublisherClient.createNameValuePair('sourceApplication', event.sourceApplication),
      AtroposPublisherClient.createNameValuePair('timestamp', event.timestamp.toISOString())
    ];

    if (event.correlationId) {
      sourceAttributes.push(
        AtroposPublisherClient.createNameValuePair('correlationId', event.correlationId)
      );
    }

    return {
      data: event,
      tenant: event.tenantId,
      tags: [tag],
      objectType,
      operationType: OperationType.CREATED,
      objectId: this.getObjectId(event),
      stateMachineState: 'NONE',
      topicScope: TopicScope.TENANT,
      sourceAttributes
    };
  }

  /**
   * Constructs PubSub event for close exception
   */
  private constructPubSubCloseEventBuilder(
    event: CloseExceptionInstanceEvent,
    objectType: string
  ): PubSubEvent {
    const tag = AtroposPublisherClient.createTag(
      'businessExceptionDefinitionCode',
      event.businessExceptionDefinitionCode
    );

    const sourceAttributes = [
      AtroposPublisherClient.createNameValuePair('businessExceptionTenantID', event.tenantId),
      AtroposPublisherClient.createNameValuePair('businessExceptionDefinitionCode', event.businessExceptionDefinitionCode),
      AtroposPublisherClient.createNameValuePair('sourceApplication', event.sourceApplication),
      AtroposPublisherClient.createNameValuePair('timestamp', event.timestamp.toISOString()),
      AtroposPublisherClient.createNameValuePair('action', event.action)
    ];

    if (event.correlationId) {
      sourceAttributes.push(
        AtroposPublisherClient.createNameValuePair('correlationId', event.correlationId)
      );
    }

    return {
      data: event,
      tenant: event.tenantId,
      tags: [tag],
      objectType,
      operationType: OperationType.UPDATED,
      objectId: this.getCloseObjectId(event),
      stateMachineState: 'CLOSED',
      topicScope: TopicScope.TENANT,
      sourceAttributes
    };
  }

  /**
   * Generates object ID for business exception event
   */
  private getObjectId(event: BusinessExceptionInstanceEvent): string {
    return `${event.tenantId}_${event.businessExceptionDefinitionCode}_${event.idempotentKey}`;
  }

  /**
   * Generates object ID for close exception event
   */
  private getCloseObjectId(event: CloseExceptionInstanceEvent): string {
    return `${event.tenantId}_${event.businessExceptionDefinitionCode}_${event.idempotentKey}_close`;
  }

  /**
   * Formats topic name
   */
  private formatTopic(topicScope: TopicScope, tenant: string, objectType: string): string {
    return `_${topicScope.toLowerCase()}_${tenant}_${objectType}`;
  }
}
