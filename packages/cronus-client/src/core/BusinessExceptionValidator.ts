import { BusinessException, CloseException, BusinessObservation } from '../models';

/**
 * Validation errors for business exceptions
 */
export class ValidationError extends Error {
  constructor(message: string, public readonly field: string) {
    super(message);
    this.name = 'ValidationError';
  }
}

/**
 * Validator for business exceptions and observations
 */
export class BusinessExceptionValidator {
  /**
   * Validates a business exception
   */
  validateBusinessException(businessException: BusinessException): void {
    this.validateRequired(businessException.getTenantId(), 'tenantId');
    this.validateRequired(businessException.getBusinessExceptionDefinitionCode(), 'businessExceptionDefinitionCode');
    this.validateRequired(businessException.getIdempotentKey(), 'idempotentKey');

    // Validate tenant ID format
    if (!this.isValidTenantId(businessException.getTenantId())) {
      throw new ValidationError('Invalid tenant ID format', 'tenantId');
    }

    // Validate idempotent key format
    if (!this.isValidIdempotentKey(businessException.getIdempotentKey())) {
      throw new ValidationError('Invalid idempotent key format', 'idempotentKey');
    }

    // Validate exception definition code
    if (!this.isValidDefinitionCode(businessException.getBusinessExceptionDefinitionCode())) {
      throw new ValidationError('Invalid business exception definition code format', 'businessExceptionDefinitionCode');
    }
  }

  /**
   * Validates a close exception
   */
  validateCloseException(closeException: CloseException): void {
    this.validateRequired(closeException.getTenantId(), 'tenantId');
    this.validateRequired(closeException.getBusinessExceptionDefinitionCode(), 'businessExceptionDefinitionCode');
    this.validateRequired(closeException.getIdempotentKey(), 'idempotentKey');
    this.validateRequired(closeException.getAction(), 'action');
    this.validateRequired(closeException.getClosureRemark(), 'closureRemark');

    // Validate tenant ID format
    if (!this.isValidTenantId(closeException.getTenantId())) {
      throw new ValidationError('Invalid tenant ID format', 'tenantId');
    }

    // Validate idempotent key format
    if (!this.isValidIdempotentKey(closeException.getIdempotentKey())) {
      throw new ValidationError('Invalid idempotent key format', 'idempotentKey');
    }

    // Validate exception definition code
    if (!this.isValidDefinitionCode(closeException.getBusinessExceptionDefinitionCode())) {
      throw new ValidationError('Invalid business exception definition code format', 'businessExceptionDefinitionCode');
    }
  }

  /**
   * Validates a business observation
   */
  validateBusinessObservation(businessObservation: BusinessObservation): void {
    this.validateRequired(businessObservation.getTenantId(), 'tenantId');
    this.validateRequired(businessObservation.getDefinitionCode(), 'definitionCode');
    this.validateRequired(businessObservation.getIdempotentKey(), 'idempotentKey');

    // Validate tenant ID format
    if (!this.isValidTenantId(businessObservation.getTenantId())) {
      throw new ValidationError('Invalid tenant ID format', 'tenantId');
    }

    // Validate idempotent key format
    if (!this.isValidIdempotentKey(businessObservation.getIdempotentKey())) {
      throw new ValidationError('Invalid idempotent key format', 'idempotentKey');
    }

    // Validate definition code
    if (!this.isValidDefinitionCode(businessObservation.getDefinitionCode())) {
      throw new ValidationError('Invalid definition code format', 'definitionCode');
    }
  }

  private validateRequired(value: any, fieldName: string): void {
    if (value === null || value === undefined || value === '') {
      throw new ValidationError(`${fieldName} is required`, fieldName);
    }
  }

  private isValidTenantId(tenantId: string): boolean {
    // Tenant ID should be alphanumeric with possible hyphens and dots
    return /^[a-zA-Z0-9.-]+$/.test(tenantId) && tenantId.length <= 100;
  }

  private isValidIdempotentKey(key: string): boolean {
    // Idempotent key should be a valid UUID or alphanumeric string
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    const alphanumericRegex = /^[a-zA-Z0-9_-]+$/;
    
    return (uuidRegex.test(key) || alphanumericRegex.test(key)) && key.length <= 255;
  }

  private isValidDefinitionCode(code: string): boolean {
    // Definition code should be alphanumeric with possible underscores and hyphens
    return /^[a-zA-Z0-9_-]+$/.test(code) && code.length <= 100;
  }
}
