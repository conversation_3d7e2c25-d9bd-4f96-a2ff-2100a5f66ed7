import { 
  BusinessObservation, 
  BusinessObservationHandler 
} from '../models/interfaces';
import { 
  BusinessObservationEvent,
  EventFactory,
  PubSubEvent,
  OperationType,
  TopicScope
} from '../models/events';
import { AtroposPublisherClient } from './AtroposPublisherClient';
import { Logger } from '../utils/Logger';

/**
 * Atropos-based implementation of business observation handler
 */
export class AtroposBusinessObservationHandler implements BusinessObservationHandler {
  private static readonly TOPIC_NAME_FORMAT = '_%s_%s_%s';
  private static readonly BUSINESS_OBSERVATION_OBJECT_TYPE = 'BusinessObservation';
  private static readonly CLOSE_OBSERVATION_OBJECT_TYPE = 'BusinessObservationClose';

  private readonly atroposPublisherClient: AtroposPublisherClient;
  private readonly logger: Logger;
  private readonly sourceApplication: string;

  constructor(
    atroposPublisherClient: AtroposPublisherClient,
    logger: Logger,
    sourceApplication: string
  ) {
    this.atroposPublisherClient = atroposPublisherClient;
    this.logger = logger;
    this.sourceApplication = sourceApplication;
  }

  /**
   * Publishes a business observation
   */
  async publish(businessObservation: BusinessObservation): Promise<void> {
    try {
      this.logger.info('Publishing business observation', {
        tenantId: businessObservation.getTenantId(),
        definitionCode: businessObservation.getDefinitionCode(),
        idempotentKey: businessObservation.getIdempotentKey()
      });

      const event = EventFactory.createBusinessObservationEvent(
        businessObservation,
        this.sourceApplication
      );

      const pubSubEvent = this.constructPubSubEventBuilder(
        event,
        AtroposBusinessObservationHandler.BUSINESS_OBSERVATION_OBJECT_TYPE
      );

      const topic = this.formatTopic(
        pubSubEvent.topicScope,
        pubSubEvent.tenant,
        pubSubEvent.objectType
      );

      await this.atroposPublisherClient.publish(pubSubEvent);

      this.logger.info('Successfully published business observation', {
        topic,
        tenantId: businessObservation.getTenantId(),
        definitionCode: businessObservation.getDefinitionCode(),
        idempotentKey: businessObservation.getIdempotentKey()
      });
    } catch (error) {
      this.logger.error('Failed to publish business observation', {
        tenantId: businessObservation.getTenantId(),
        definitionCode: businessObservation.getDefinitionCode(),
        idempotentKey: businessObservation.getIdempotentKey(),
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Resolves a business observation
   */
  async resolve(businessObservation: BusinessObservation): Promise<void> {
    try {
      this.logger.info('Resolving business observation', {
        tenantId: businessObservation.getTenantId(),
        definitionCode: businessObservation.getDefinitionCode(),
        idempotentKey: businessObservation.getIdempotentKey()
      });

      const event = EventFactory.createBusinessObservationEvent(
        businessObservation,
        this.sourceApplication
      );

      const pubSubEvent = this.constructPubSubEventBuilder(
        event,
        AtroposBusinessObservationHandler.CLOSE_OBSERVATION_OBJECT_TYPE
      );

      const topic = this.formatTopic(
        pubSubEvent.topicScope,
        pubSubEvent.tenant,
        pubSubEvent.objectType
      );

      await this.atroposPublisherClient.publish(pubSubEvent);

      this.logger.info('Successfully resolved business observation', {
        topic,
        tenantId: businessObservation.getTenantId(),
        definitionCode: businessObservation.getDefinitionCode(),
        idempotentKey: businessObservation.getIdempotentKey()
      });
    } catch (error) {
      this.logger.error('Failed to resolve business observation', {
        tenantId: businessObservation.getTenantId(),
        definitionCode: businessObservation.getDefinitionCode(),
        idempotentKey: businessObservation.getIdempotentKey(),
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Constructs PubSub event for business observation
   */
  private constructPubSubEventBuilder(
    event: BusinessObservationEvent,
    objectType: string
  ): PubSubEvent {
    const tag = AtroposPublisherClient.createTag(
      'definitionCode',
      event.definitionCode
    );

    const sourceAttributes = [
      AtroposPublisherClient.createNameValuePair('tenantId', event.tenantId),
      AtroposPublisherClient.createNameValuePair('definitionCode', event.definitionCode),
      AtroposPublisherClient.createNameValuePair('sourceApplication', event.sourceApplication),
      AtroposPublisherClient.createNameValuePair('timestamp', event.timestamp.toISOString())
    ];

    if (event.correlationId) {
      sourceAttributes.push(
        AtroposPublisherClient.createNameValuePair('correlationId', event.correlationId)
      );
    }

    return {
      data: event,
      tenant: event.tenantId,
      tags: [tag],
      objectType,
      operationType: objectType.includes('Close') ? OperationType.UPDATED : OperationType.CREATED,
      objectId: this.getObjectId(event),
      stateMachineState: objectType.includes('Close') ? 'RESOLVED' : 'NONE',
      topicScope: TopicScope.TENANT,
      sourceAttributes
    };
  }

  /**
   * Generates object ID for business observation event
   */
  private getObjectId(event: BusinessObservationEvent): string {
    return `${event.tenantId}_${event.definitionCode}_${event.idempotentKey}`;
  }

  /**
   * Formats topic name
   */
  private formatTopic(topicScope: TopicScope, tenant: string, objectType: string): string {
    return `_${topicScope.toLowerCase()}_${tenant}_${objectType}`;
  }
}
