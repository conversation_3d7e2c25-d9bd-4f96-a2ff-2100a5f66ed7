import { 
  BusinessException, 
  CloseException, 
  Publisher 
} from '../models/interfaces';
import { 
  BusinessExceptionInstanceEvent, 
  CloseExceptionInstanceEvent,
  EventFactory 
} from '../models/events';
import { BusinessExceptionValidator } from './BusinessExceptionValidator';
import { Logger } from '../utils/Logger';

/**
 * Abstract base class for business exception handlers
 */
export abstract class AbstractBusinessExceptionHandler implements Publisher {
  protected readonly businessExceptionValidator: BusinessExceptionValidator;
  protected readonly logger: Logger;
  protected readonly sourceApplication: string;

  constructor(
    businessExceptionValidator: BusinessExceptionValidator,
    logger: Logger,
    sourceApplication: string
  ) {
    this.businessExceptionValidator = businessExceptionValidator;
    this.logger = logger;
    this.sourceApplication = sourceApplication;
  }

  /**
   * Publishes a business exception
   */
  async publish(businessException: BusinessException): Promise<void> {
    try {
      this.logger.info('Publishing business exception', {
        tenantId: businessException.getTenantId(),
        definitionCode: businessException.getBusinessExceptionDefinitionCode(),
        idempotentKey: businessException.getIdempotentKey()
      });

      const enrichedEvent = await this.enrichBusinessExceptionInstance(businessException);
      await this.publishBusinessExceptionInstanceEvent(enrichedEvent);

      this.logger.info('Successfully published business exception', {
        tenantId: businessException.getTenantId(),
        definitionCode: businessException.getBusinessExceptionDefinitionCode(),
        idempotentKey: businessException.getIdempotentKey()
      });
    } catch (error) {
      this.logger.error('Failed to publish business exception', {
        tenantId: businessException.getTenantId(),
        definitionCode: businessException.getBusinessExceptionDefinitionCode(),
        idempotentKey: businessException.getIdempotentKey(),
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Closes/resolves a business exception
   */
  async close(closeException: CloseException): Promise<void> {
    try {
      this.logger.info('Closing business exception', {
        tenantId: closeException.getTenantId(),
        definitionCode: closeException.getBusinessExceptionDefinitionCode(),
        idempotentKey: closeException.getIdempotentKey(),
        action: closeException.getAction()
      });

      const enrichedEvent = await this.enrichCloseExceptionInstance(closeException);
      await this.publishCloseExceptionInstanceEvent(enrichedEvent);

      this.logger.info('Successfully closed business exception', {
        tenantId: closeException.getTenantId(),
        definitionCode: closeException.getBusinessExceptionDefinitionCode(),
        idempotentKey: closeException.getIdempotentKey(),
        action: closeException.getAction()
      });
    } catch (error) {
      this.logger.error('Failed to close business exception', {
        tenantId: closeException.getTenantId(),
        definitionCode: closeException.getBusinessExceptionDefinitionCode(),
        idempotentKey: closeException.getIdempotentKey(),
        action: closeException.getAction(),
        error: error instanceof Error ? error.message : String(error)
      });
      throw error;
    }
  }

  /**
   * Enriches a business exception instance with additional data
   */
  protected async enrichBusinessExceptionInstance(
    businessException: BusinessException
  ): Promise<BusinessExceptionInstanceEvent> {
    // Validate the business exception
    this.businessExceptionValidator.validateBusinessException(businessException);

    // Create the event
    const event = EventFactory.createBusinessExceptionInstanceEvent(
      businessException,
      this.sourceApplication
    );

    // Add any additional enrichment logic here
    return event;
  }

  /**
   * Enriches a close exception instance with additional data
   */
  protected async enrichCloseExceptionInstance(
    closeException: CloseException
  ): Promise<CloseExceptionInstanceEvent> {
    // Validate the close exception
    this.businessExceptionValidator.validateCloseException(closeException);

    // Create the event
    const event = EventFactory.createCloseExceptionInstanceEvent(
      closeException,
      this.sourceApplication
    );

    // Add any additional enrichment logic here
    return event;
  }

  /**
   * Abstract method to publish business exception instance event
   * Must be implemented by concrete classes
   */
  protected abstract publishBusinessExceptionInstanceEvent(
    event: BusinessExceptionInstanceEvent
  ): Promise<void>;

  /**
   * Abstract method to publish close exception instance event
   * Must be implemented by concrete classes
   */
  protected abstract publishCloseExceptionInstanceEvent(
    event: CloseExceptionInstanceEvent
  ): Promise<void>;
}
